import 'dart:async';
import 'dart:convert';

import 'package:flockin_app/services/attendance_service.dart';
import 'package:flockin_app/services/church_service.dart';
import 'package:flockin_app/services/geolocation_service.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

/// Service for managing automatic attendance based on location
class AutomaticAttendanceService {
  static const String _backgroundTaskName = 'automatic_attendance_check';
  static const String _lastLocationKey = 'last_known_location';
  static const String _attendanceStatusKey = 'attendance_status';
  static const String _isServiceEnabledKey = 'auto_attendance_enabled';

  static const Duration _locationCheckInterval = Duration(minutes: 5);
  static const Duration _backgroundTaskInterval = Duration(minutes: 10);

  final AttendanceService _attendanceService = AttendanceService();
  final GeolocationService _geolocationService = GeolocationService();

  StreamSubscription<Position>? _locationSubscription;
  Timer? _periodicTimer;

  // Stream controllers for real-time updates
  final StreamController<AttendanceStatus> _statusController =
      StreamController<AttendanceStatus>.broadcast();
  final StreamController<LocationData?> _locationController =
      StreamController<LocationData?>.broadcast();

  Stream<AttendanceStatus> get statusStream => _statusController.stream;
  Stream<LocationData?> get locationStream => _locationController.stream;

  /// Initialize the automatic attendance service
  Future<void> initialize() async {
    if (kDebugMode) {
      print('Initializing AutomaticAttendanceService');
    }

    // Initialize workmanager
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );

    // Check if service is enabled
    final isEnabled = await isServiceEnabled();
    if (isEnabled) {
      await _startLocationMonitoring();
      await _scheduleBackgroundTasks();
    }
  }

  /// Enable automatic attendance service
  Future<void> enableService() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isServiceEnabledKey, true);

    await _startLocationMonitoring();
    await _scheduleBackgroundTasks();

    if (kDebugMode) {
      print('Automatic attendance service enabled');
    }
  }

  /// Disable automatic attendance service
  Future<void> disableService() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isServiceEnabledKey, false);

    await _stopLocationMonitoring();
    await Workmanager().cancelAll();

    if (kDebugMode) {
      print('Automatic attendance service disabled');
    }
  }

  /// Check if service is enabled
  Future<bool> isServiceEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isServiceEnabledKey) ?? false;
  }

  /// Get current attendance status
  Future<AttendanceStatus> getCurrentStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final statusJson = prefs.getString(_attendanceStatusKey);

    if (statusJson != null) {
      try {
        final statusMap = jsonDecode(statusJson) as Map<String, dynamic>;
        return AttendanceStatus.fromJson(statusMap);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing attendance status: $e');
        }
      }
    }

    return AttendanceStatus.absent();
  }

  /// Get last known location
  Future<LocationData?> getLastKnownLocation() async {
    final prefs = await SharedPreferences.getInstance();
    final locationJson = prefs.getString(_lastLocationKey);

    if (locationJson != null) {
      try {
        final locationMap = jsonDecode(locationJson) as Map<String, dynamic>;
        return LocationData.fromJson(locationMap);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing last location: $e');
        }
      }
    }

    return null;
  }

  /// Start location monitoring
  Future<void> _startLocationMonitoring() async {
    // Check permissions first
    final hasPermissions = await _geolocationService
        .checkAndRequestPermissions();
    if (!hasPermissions) {
      if (kDebugMode) {
        print('Location permissions not granted');
      }
      return;
    }

    // Start location updates
    _locationSubscription = _geolocationService.startLocationUpdates(
      onLocationUpdate: _handleLocationUpdate,
      onError: (error) {
        if (kDebugMode) {
          print('Location update error: $error');
        }
      },
      intervalMs: _locationCheckInterval.inMilliseconds,
    );

    // Start periodic attendance checks
    _periodicTimer = Timer.periodic(_locationCheckInterval, (_) {
      _performAttendanceCheck();
    });

    if (kDebugMode) {
      print('Location monitoring started');
    }
  }

  /// Stop location monitoring
  Future<void> _stopLocationMonitoring() async {
    _locationSubscription?.cancel();
    _locationSubscription = null;

    _periodicTimer?.cancel();
    _periodicTimer = null;

    if (kDebugMode) {
      print('Location monitoring stopped');
    }
  }

  /// Handle location updates
  Future<void> _handleLocationUpdate(LocationData location) async {
    // Save location to preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastLocationKey, jsonEncode(location.toJson()));

    // Emit location update
    _locationController.add(location);

    // Perform attendance check
    await _performAttendanceCheck();
  }

  /// Perform attendance check based on current location
  Future<void> _performAttendanceCheck() async {
    try {
      final location = await _geolocationService.getCurrentLocation();
      if (location == null) {
        if (kDebugMode) {
          print('No location available for attendance check');
        }
        return;
      }

      // Validate location for check-in
      final validation = await _attendanceService.validateLocationForCheckIn();
      if (validation == null) {
        if (kDebugMode) {
          print('Location validation failed');
        }
        return;
      }

      final currentStatus = await getCurrentStatus();

      // Check if user is within geofence and not already checked in
      if (validation.canCheckIn && !currentStatus.isPresent) {
        await _performAutomaticCheckIn(location);
      } else if (!validation.canCheckIn && currentStatus.isPresent) {
        // User has left the geofence - update status to absent
        await updateAttendanceStatus(AttendanceStatus.absent());
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during attendance check: $e');
      }
    }
  }

  /// Perform automatic check-in
  Future<void> _performAutomaticCheckIn(LocationData location) async {
    try {
      final result = await _attendanceService.checkIn();
      if (result != null) {
        final status = AttendanceStatus.present(
          checkInTime: DateTime.now(),
          location: location,
        );

        await updateAttendanceStatus(status);

        if (kDebugMode) {
          print('Automatic check-in successful');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Automatic check-in failed: $e');
      }

      // Handle offline scenario - save pending check-in
      await _savePendingCheckIn(location);
    }
  }

  /// Update attendance status
  Future<void> updateAttendanceStatus(AttendanceStatus status) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_attendanceStatusKey, jsonEncode(status.toJson()));

    // Emit status update
    _statusController.add(status);
  }

  /// Save pending check-in for offline scenarios
  Future<void> _savePendingCheckIn(LocationData location) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingCheckIn = {
      'location': location.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    };
    await prefs.setString('pending_checkin', jsonEncode(pendingCheckIn));
  }

  /// Schedule background tasks
  Future<void> _scheduleBackgroundTasks() async {
    // Cancel existing tasks
    await Workmanager().cancelAll();

    // Schedule periodic background attendance check
    await Workmanager().registerPeriodicTask(
      _backgroundTaskName,
      _backgroundTaskName,
      frequency: _backgroundTaskInterval,
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
    );

    if (kDebugMode) {
      print('Background tasks scheduled');
    }
  }

  /// Refresh church data and restart location monitoring
  Future<void> refreshChurchData(Church church) async {
    try {
      // Stop current monitoring
      await _stopLocationMonitoring();

      // Clear any cached data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastLocationKey);
      await prefs.remove(_attendanceStatusKey);

      // Restart with new church data
      await _startLocationMonitoring();

      if (kDebugMode) {
        print('🔄 Church data refreshed and location monitoring restarted');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing church data: $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _locationSubscription?.cancel();
    _periodicTimer?.cancel();
    _statusController.close();
    _locationController.close();
  }
}

/// Attendance status model
class AttendanceStatus {
  final bool isPresent;
  final DateTime? checkInTime;
  final LocationData? location;
  final String message;

  AttendanceStatus({
    required this.isPresent,
    this.checkInTime,
    this.location,
    required this.message,
  });

  factory AttendanceStatus.present({
    required DateTime checkInTime,
    required LocationData location,
  }) {
    return AttendanceStatus(
      isPresent: true,
      checkInTime: checkInTime,
      location: location,
      message: 'Present at church',
    );
  }

  factory AttendanceStatus.absent() {
    return AttendanceStatus(isPresent: false, message: 'Not at church');
  }

  factory AttendanceStatus.fromJson(Map<String, dynamic> json) {
    return AttendanceStatus(
      isPresent: json['isPresent'] ?? false,
      checkInTime: json['checkInTime'] != null
          ? DateTime.parse(json['checkInTime'])
          : null,
      location: json['location'] != null
          ? LocationData.fromJson(json['location'])
          : null,
      message: json['message'] ?? 'Unknown status',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isPresent': isPresent,
      'checkInTime': checkInTime?.toIso8601String(),
      'location': location?.toJson(),
      'message': message,
    };
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    if (task == AutomaticAttendanceService._backgroundTaskName) {
      // Perform background attendance check
      final service = AutomaticAttendanceService();
      await service._performAttendanceCheck();
      return Future.value(true);
    }
    return Future.value(false);
  });
}
