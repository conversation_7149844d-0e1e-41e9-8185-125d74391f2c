import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';
import 'package:flockin_mobile/utils/constants.dart';

class ServiceTime {
  final String day;
  final String startTime;
  final String endTime;
  final String name;

  ServiceTime({
    required this.day,
    required this.startTime,
    required this.endTime,
    required this.name,
  });

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'startTime': startTime,
      'endTime': endTime,
      'name': name,
    };
  }

  factory ServiceTime.fromJson(Map<String, dynamic> json) {
    return ServiceTime(
      day: json['day'],
      startTime: json['startTime'],
      endTime: json['endTime'],
      name: json['name'],
    );
  }
}

class Church {
  final String id;
  final String name;
  final String? branch;
  final String? description;
  final String address;
  final double? latitude;
  final double? longitude;
  final int geofenceRadius;
  final String? phone;
  final String? email;
  final String adminName;
  final String adminPhone;
  final String adminEmail;
  final List<ServiceTime> serviceTimes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Church({
    required this.id,
    required this.name,
    this.branch,
    this.description,
    required this.address,
    this.latitude,
    this.longitude,
    required this.geofenceRadius,
    this.phone,
    this.email,
    required this.adminName,
    required this.adminPhone,
    required this.adminEmail,
    required this.serviceTimes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Church.fromJson(Map<String, dynamic> json) {
    return Church(
      id: json['id'],
      name: json['name'],
      branch: json['branch'],
      description: json['description'],
      address: json['address'],
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      geofenceRadius: _parseInt(json['geofenceRadius']) ?? 100,
      phone: json['phone'],
      email: json['email'],
      adminName: json['adminName'],
      adminPhone: json['adminPhone'],
      adminEmail: json['adminEmail'],
      serviceTimes:
          (json['serviceTimes'] as List<dynamic>?)
              ?.map((item) => ServiceTime.fromJson(item))
              .toList() ??
          [],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Helper method to safely parse double values from JSON
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Helper method to safely parse int values from JSON
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}

class QuickChurchSetup {
  final String name;
  final String? branch;
  final String address;
  final String adminName;
  final String adminPhone;
  final String adminEmail;
  final String password;
  final String confirmPassword;

  QuickChurchSetup({
    required this.name,
    this.branch,
    required this.address,
    required this.adminName,
    required this.adminPhone,
    required this.adminEmail,
    required this.password,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'branch': branch,
      'address': address,
      'adminName': adminName,
      'adminPhone': adminPhone,
      'adminEmail': adminEmail,
      'password': password,
      'confirmPassword': confirmPassword,
    };
  }
}

class ChurchSetupResponse {
  final String message;
  final Church church;

  ChurchSetupResponse({required this.message, required this.church});

  factory ChurchSetupResponse.fromJson(Map<String, dynamic> json) {
    return ChurchSetupResponse(
      message: json['message'],
      church: Church.fromJson(json['church']),
    );
  }
}

class ChurchService {
  final GeolocationService _geolocationService = GeolocationService();

  /// Get authentication token from storage
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  /// Get common headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Quick church setup for mobile-first approach
  Future<ChurchSetupResponse?> quickSetup({
    required String name,
    String? branch,
    required String address,
    required String adminName,
    required String adminPhone,
    required String adminEmail,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final setup = QuickChurchSetup(
        name: name,
        branch: branch,
        address: address,
        adminName: adminName,
        adminPhone: adminPhone,
        adminEmail: adminEmail,
        password: password,
        confirmPassword: confirmPassword,
      );

      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/churches/quick-setup'),
        headers: await _getHeaders(),
        body: jsonEncode(setup.toJson()),
      );

      if (response.statusCode == 201) {
        return ChurchSetupResponse.fromJson(jsonDecode(response.body));
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Church setup failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Church setup error: $e');
      }
      rethrow;
    }
  }

  /// Update church location
  Future<ChurchSetupResponse?> updateLocation({
    required String churchId,
    required double latitude,
    required double longitude,
    int? geofenceRadius,
  }) async {
    try {
      // Validate coordinates
      if (!_geolocationService.isValidCoordinates(latitude, longitude)) {
        throw Exception('Invalid coordinates provided');
      }

      final locationData = {
        'latitude': latitude,
        'longitude': longitude,
        if (geofenceRadius != null) 'geofenceRadius': geofenceRadius,
      };

      final response = await http.patch(
        Uri.parse('${AppConstants.baseUrl}/churches/$churchId/location'),
        headers: await _getHeaders(),
        body: jsonEncode(locationData),
      );

      if (response.statusCode == 200) {
        return ChurchSetupResponse.fromJson(jsonDecode(response.body));
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Location update failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Location update error: $e');
      }
      rethrow;
    }
  }

  /// Get all churches
  Future<List<Church>> getAllChurches() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/churches'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Church.fromJson(json)).toList();
      } else {
        throw Exception('Failed to get churches');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get churches error: $e');
      }
      rethrow;
    }
  }

  /// Find churches near a location
  Future<List<Church>> findNearbyChurches({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AppConstants.baseUrl}/churches/nearby?latitude=$latitude&longitude=$longitude&radius=$radiusKm',
        ),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Church.fromJson(json)).toList();
      } else {
        throw Exception('Failed to find nearby churches');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Find nearby churches error: $e');
      }
      rethrow;
    }
  }

  /// Get church by ID
  Future<Church?> getChurchById(String churchId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/churches/$churchId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        return Church.fromJson(jsonDecode(response.body));
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get church');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get church error: $e');
      }
      rethrow;
    }
  }

  /// Calculate distance to church
  double? calculateDistanceToChurch(
    Church church,
    double userLat,
    double userLng,
  ) {
    // Return null if church coordinates are not set
    if (church.latitude == null || church.longitude == null) {
      return null;
    }

    return _geolocationService.calculateDistance(
      userLat,
      userLng,
      church.latitude!,
      church.longitude!,
    );
  }

  /// Check if user is within church geofence
  bool isWithinChurchGeofence(Church church, double userLat, double userLng) {
    final distance = calculateDistanceToChurch(church, userLat, userLng);
    if (distance == null) {
      return false; // Can't check geofence without coordinates
    }
    return distance <= church.geofenceRadius;
  }

  /// Format distance for display
  String formatDistance(double distanceMeters) {
    return _geolocationService.formatDistance(distanceMeters);
  }

  /// Dispose resources
  void dispose() {
    _geolocationService.dispose();
  }
}
