import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/widgets/common/gradient_container.dart';

/// Unified attendance card that combines service info with attendance status
/// This replaces both TodaysServiceCard and separate attendance indicators

class AttendanceStatusIndicator extends StatefulWidget {
  final AutomaticAttendanceService attendanceService;
  final VoidCallback? onStatusTap;

  const AttendanceStatusIndicator({
    super.key,
    required this.attendanceService,
    this.onStatusTap,
  });

  @override
  State<AttendanceStatusIndicator> createState() =>
      _AttendanceStatusIndicatorState();
}

class _AttendanceStatusIndicatorState extends State<AttendanceStatusIndicator>
    with TickerProviderStateMixin {
  AttendanceStatus _currentStatus = AttendanceStatus.absent();
  bool _isLoading = true;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize pulse animation for loading state
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _initializeStatus();
    _listenToStatusUpdates();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _initializeStatus() async {
    try {
      final status = await widget.attendanceService.getCurrentStatus();
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _listenToStatusUpdates() {
    widget.attendanceService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    });
  }

  LinearGradient _getStatusGradient() {
    if (_isLoading) {
      return LinearGradient(
        colors: [Colors.grey.shade400, Colors.grey.shade500],
      );
    }

    if (_currentStatus.isPresent) {
      return const LinearGradient(
        colors: [Color(0xFF4CAF50), Color(0xFF45A049)], // Green gradient
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    } else {
      return const LinearGradient(
        colors: [Color(0xFFFF5722), Color(0xFFE64A19)], // Red gradient
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
  }

  IconData _getStatusIcon() {
    if (_isLoading) {
      return Icons.sync;
    }
    return _currentStatus.isPresent ? Icons.check_circle : Icons.location_off;
  }

  String _getStatusText() {
    if (_isLoading) {
      return 'Checking Status...';
    }
    return _currentStatus.isPresent ? 'Present' : 'Absent';
  }

  String _getStatusSubtext() {
    if (_isLoading) {
      return 'Detecting location...';
    }

    if (_currentStatus.isPresent && _currentStatus.checkInTime != null) {
      final timeFormat = DateFormat('hh:mm a');
      return 'Checked in at ${timeFormat.format(_currentStatus.checkInTime!)}';
    }

    return _currentStatus.message;
  }

  Widget _buildStatusIcon() {
    if (_isLoading) {
      return AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          _pulseController.repeat(reverse: true);
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Icon(_getStatusIcon(), color: Colors.white, size: 28),
          );
        },
      );
    }

    return Icon(_getStatusIcon(), color: Colors.white, size: 28);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedGradientContainer(
      gradient: _getStatusGradient(),
      borderRadius: BorderRadius.circular(20),
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 24),
      onTap: widget.onStatusTap,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatusIcon(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusText(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getStatusSubtext(),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!_isLoading) ...[
            const SizedBox(height: 16),
            _buildStatusDetails(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.white.withValues(alpha: 0.8),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _currentStatus.isPresent
                  ? 'Attendance is being tracked automatically based on your location'
                  : 'You will be automatically checked in when you arrive at church',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact version of the attendance status indicator for smaller spaces
class CompactAttendanceStatusIndicator extends StatefulWidget {
  final AutomaticAttendanceService attendanceService;
  final VoidCallback? onTap;

  const CompactAttendanceStatusIndicator({
    super.key,
    required this.attendanceService,
    this.onTap,
  });

  @override
  State<CompactAttendanceStatusIndicator> createState() =>
      _CompactAttendanceStatusIndicatorState();
}

class _CompactAttendanceStatusIndicatorState
    extends State<CompactAttendanceStatusIndicator> {
  AttendanceStatus _currentStatus = AttendanceStatus.absent();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeStatus();
    _listenToStatusUpdates();
  }

  Future<void> _initializeStatus() async {
    try {
      final status = await widget.attendanceService.getCurrentStatus();
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _listenToStatusUpdates() {
    widget.attendanceService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _isLoading
              ? Colors.grey.shade400
              : _currentStatus.isPresent
              ? Colors.green
              : Colors.red,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _isLoading
                  ? Icons.sync
                  : _currentStatus.isPresent
                  ? Icons.check_circle
                  : Icons.location_off,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              _isLoading
                  ? 'Checking...'
                  : _currentStatus.isPresent
                  ? 'Present'
                  : 'Absent',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
