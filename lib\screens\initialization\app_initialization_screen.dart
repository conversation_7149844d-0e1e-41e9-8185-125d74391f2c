import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flockin_app/screens/auth/login_screen.dart';
import 'package:flockin_app/screens/main/main_screen.dart';
import 'package:flockin_app/services/auth_service.dart';
import 'package:flockin_app/services/app_initialization_service.dart';
import 'package:flockin_app/services/automatic_attendance_service.dart';
import 'package:flockin_app/services/geolocation_service.dart';
import 'package:flockin_app/utils/colors.dart';

class AppInitializationScreen extends StatefulWidget {
  const AppInitializationScreen({super.key});

  @override
  State<AppInitializationScreen> createState() =>
      _AppInitializationScreenState();
}

class _AppInitializationScreenState extends State<AppInitializationScreen> {
  late AppInitializationService _initializationService;
  bool _isInitializing = true;
  String _statusMessage = 'Initializing automatic attendance...';
  AppInitializationResult? _result;

  @override
  void initState() {
    super.initState();
    _checkAuthenticationAndInitialize();
  }

  Future<void> _checkAuthenticationAndInitialize() async {
    setState(() {
      _statusMessage = 'Checking authentication...';
    });

    final authService = Provider.of<AuthService>(context, listen: false);

    // Check if user is already authenticated
    final isAuthenticated = await authService.checkAuthStatus();

    if (!mounted) return;

    if (!isAuthenticated) {
      // Navigate to login screen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
      return;
    }

    // User is authenticated, proceed with location services initialization
    await _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _statusMessage = 'Setting up location services...';
    });

    // Create service instances
    final automaticAttendanceService = AutomaticAttendanceService();
    final geolocationService = GeolocationService();

    _initializationService = AppInitializationService(
      automaticAttendanceService: automaticAttendanceService,
      geolocationService: geolocationService,
    );

    // Start initialization
    await _performInitialization();
  }

  Future<void> _performInitialization() async {
    setState(() {
      _isInitializing = true;
      _statusMessage = 'Setting up automatic attendance...';
    });

    try {
      final result = await _initializationService.initializeApp();

      if (mounted) {
        setState(() {
          _result = result;
          _isInitializing = false;
          _statusMessage = result.message;
        });

        // Handle different results
        if (result.isSuccess) {
          _handleSuccessfulInitialization();
        } else if (result.isPermissionDenied) {
          _handlePermissionDenied();
        } else {
          _handleInitializationFailed();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isInitializing = false;
          _statusMessage = 'Initialization failed: ${e.toString()}';
        });
      }
    }
  }

  void _handleSuccessfulInitialization() {
    // Show success message briefly, then navigate to main app
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    });
  }

  void _handlePermissionDenied() {
    // Show permission explanation and retry option
    // For now, just show the status
  }

  void _handleInitializationFailed() {
    // Show error and retry option
    // For now, just show the status
  }

  Future<void> _requestPermissions() async {
    final shouldRequest = await _initializationService
        .showPermissionRequestDialog(context);

    if (shouldRequest) {
      await _performInitialization();
    }
  }

  Future<void> _openSettings() async {
    await _initializationService.showSettingsDialog(context);
    // Check if permissions were granted after returning from settings
    await _performInitialization();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App logo or icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: const Icon(
                    Icons.location_on,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 32),

                // Title
                const Text(
                  'Setting Up Automatic Attendance',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Status message
                Text(
                  _statusMessage,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Loading indicator or action buttons
                if (_isInitializing)
                  const Column(
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      SizedBox(height: 16),
                      Text(
                        'This may take a few moments...',
                        style: TextStyle(color: Colors.white70, fontSize: 14),
                      ),
                    ],
                  )
                else
                  _buildActionButtons(),

                const SizedBox(height: 48),

                // Information about automatic attendance
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Automatic Attendance Features:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        'Automatic check-in when you arrive at church',
                      ),
                      _buildFeatureItem(
                        'No manual attendance marking required',
                      ),
                      _buildFeatureItem('Battery-optimized location tracking'),
                      _buildFeatureItem('Secure and private location handling'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_result == null) return const SizedBox.shrink();

    if (_result!.isSuccess) {
      return Column(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 48),
          const SizedBox(height: 16),
          const Text(
            'Setup Complete!',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Redirecting to dashboard...',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
        ],
      );
    }

    if (_result!.isPermissionDenied) {
      return Column(
        children: [
          const Icon(Icons.location_off, color: Colors.orange, size: 48),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _requestPermissions,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.blue.shade700,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text('Grant Location Permission'),
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: _openSettings,
            child: const Text(
              'Open App Settings',
              style: TextStyle(color: Colors.white70),
            ),
          ),
        ],
      );
    }

    // Failed initialization
    return Column(
      children: [
        const Icon(Icons.error, color: Colors.red, size: 48),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _performInitialization,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: Colors.blue.shade700,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          child: const Text('Retry Setup'),
        ),
        const SizedBox(height: 12),
        TextButton(
          onPressed: () {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const MainScreen()),
            );
          },
          child: const Text(
            'Continue Without Automatic Attendance',
            style: TextStyle(color: Colors.white70),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.check, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
