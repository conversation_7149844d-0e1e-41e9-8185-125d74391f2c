import 'package:intl/intl.dart';

class Attendance {
  final String id;
  final String memberId;
  final String churchId;
  final DateTime checkInTime;
  final DateTime? checkOutTime;
  final String status;
  final String serviceType;
  final double? locationAccuracy;
  final double? latitude;
  final double? longitude;
  final String? notes;

  Attendance({
    required this.id,
    required this.memberId,
    required this.churchId,
    required this.checkInTime,
    this.checkOutTime,
    required this.status,
    required this.serviceType,
    this.locationAccuracy,
    this.latitude,
    this.longitude,
    this.notes,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      id: json['id'] ?? '',
      memberId: json['memberId'] ?? json['member_id'] ?? '',
      churchId: json['churchId'] ?? json['church_id'] ?? '',
      checkInTime: DateTime.parse(
        json['checkInTime'] ??
            json['check_in_time'] ??
            DateTime.now().toIso8601String(),
      ),
      checkOutTime: (json['checkOutTime'] ?? json['check_out_time']) != null
          ? DateTime.parse(json['checkOutTime'] ?? json['check_out_time'])
          : null,
      status: json['status'] ?? 'present',
      serviceType:
          json['serviceName'] ?? json['service_type'] ?? 'Sunday Service',
      locationAccuracy: (json['locationAccuracy'] ?? json['location_accuracy'])
          ?.toDouble(),
      latitude: (json['checkInLatitude'] ?? json['latitude'])?.toDouble(),
      longitude: (json['checkInLongitude'] ?? json['longitude'])?.toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'member_id': memberId,
      'church_id': churchId,
      'check_in_time': checkInTime.toIso8601String(),
      'check_out_time': checkOutTime?.toIso8601String(),
      'status': status,
      'service_type': serviceType,
      'location_accuracy': locationAccuracy,
      'latitude': latitude,
      'longitude': longitude,
      'notes': notes,
    };
  }

  Attendance copyWith({
    String? id,
    String? memberId,
    String? churchId,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    String? status,
    String? serviceType,
    double? locationAccuracy,
    double? latitude,
    double? longitude,
    String? notes,
  }) {
    return Attendance(
      id: id ?? this.id,
      memberId: memberId ?? this.memberId,
      churchId: churchId ?? this.churchId,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      status: status ?? this.status,
      serviceType: serviceType ?? this.serviceType,
      locationAccuracy: locationAccuracy ?? this.locationAccuracy,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      notes: notes ?? this.notes,
    );
  }

  String get formattedCheckInTime {
    return DateFormat('hh:mm a').format(checkInTime);
  }

  String get formattedCheckOutTime {
    if (checkOutTime == null) return '';
    return DateFormat('hh:mm a').format(checkOutTime!);
  }

  String get formattedDate {
    return DateFormat('MMM dd, yyyy').format(checkInTime);
  }

  String get dayOfWeek {
    return DateFormat('EEEE').format(checkInTime);
  }

  bool get isCheckedOut => checkOutTime != null;

  Duration? get duration {
    if (checkOutTime == null) return null;
    return checkOutTime!.difference(checkInTime);
  }

  String get formattedDuration {
    final dur = duration;
    if (dur == null) return '';

    final hours = dur.inHours;
    final minutes = dur.inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  String toString() {
    return 'Attendance{id: $id, status: $status, checkInTime: $checkInTime, serviceType: $serviceType}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Attendance && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class AttendanceStats {
  final int totalServices;
  final int servicesAttended;
  final int currentStreak;
  final double attendanceRate;
  final int thisMonthAttendance;
  final int lastMonthAttendance;

  AttendanceStats({
    required this.totalServices,
    required this.servicesAttended,
    required this.currentStreak,
    required this.attendanceRate,
    required this.thisMonthAttendance,
    required this.lastMonthAttendance,
  });

  factory AttendanceStats.fromJson(Map<String, dynamic> json) {
    return AttendanceStats(
      totalServices: json['total_services'] ?? 0,
      servicesAttended: json['services_attended'] ?? 0,
      currentStreak: json['current_streak'] ?? 0,
      attendanceRate: (json['attendance_rate'] ?? 0.0).toDouble(),
      thisMonthAttendance: json['this_month_attendance'] ?? 0,
      lastMonthAttendance: json['last_month_attendance'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_services': totalServices,
      'services_attended': servicesAttended,
      'current_streak': currentStreak,
      'attendance_rate': attendanceRate,
      'this_month_attendance': thisMonthAttendance,
      'last_month_attendance': lastMonthAttendance,
    };
  }

  String get formattedAttendanceRate {
    return '${(attendanceRate * 100).round()}%';
  }
}
