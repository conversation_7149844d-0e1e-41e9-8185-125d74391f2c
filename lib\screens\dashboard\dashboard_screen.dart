import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flockin_app/screens/settings/settings_screen.dart';
import 'package:flockin_app/services/auth_service.dart';
import 'package:flockin_app/services/automatic_attendance_service.dart';
import 'package:flockin_app/services/church_service.dart';
import 'package:flockin_app/utils/colors.dart';
import 'package:flockin_app/utils/constants.dart';
import 'package:flockin_app/widgets/dashboard/dashboard_header.dart';
import 'package:flockin_app/widgets/dashboard/recent_attendance_card.dart';
import 'package:flockin_app/widgets/dashboard/unified_attendance_card.dart';
import 'package:flockin_app/widgets/dashboard/videos_card.dart';
import 'package:flockin_app/widgets/distance/live_distance_widget.dart';

class DashboardScreen extends StatefulWidget {
  final VoidCallback? onNavigateToVideos;

  const DashboardScreen({super.key, this.onNavigateToVideos});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late AutomaticAttendanceService _automaticAttendanceService;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  Church? _cachedChurch;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _automaticAttendanceService = AutomaticAttendanceService();
    _automaticAttendanceService.initialize();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    // Load church data initially
    final authService = Provider.of<AuthService>(context, listen: false);
    final user = authService.currentUser;
    if (user?.churchId != null) {
      try {
        _cachedChurch = await ChurchService().getChurchById(user!.churchId!);
        if (mounted) setState(() {});
      } catch (e) {
        // Handle error silently for initial load
      }
    }
  }

  Future<void> _onRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      // Refresh church data
      final authService = Provider.of<AuthService>(context, listen: false);
      final user = authService.currentUser;
      if (user?.churchId != null) {
        _cachedChurch = await ChurchService().getChurchById(user!.churchId!);

        // Force refresh of automatic attendance service with new church data
        if (_cachedChurch != null) {
          await _automaticAttendanceService.refreshChurchData(_cachedChurch!);
        }
      }

      // Add a small delay to show the refresh indicator
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // Handle refresh error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _automaticAttendanceService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final user = authService.currentUser;

        if (user == null) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.backgroundGradient,
            ),
            child: SafeArea(
              child: RefreshIndicator(
                key: _refreshIndicatorKey,
                onRefresh: _onRefresh,
                color: AppColors.primary,
                backgroundColor: Colors.white,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DashboardHeader(
                        user: user,
                        onNotificationPressed: () {},
                        onSettingsPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const SettingsScreen(),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 32),

                      // Today's Service Status - Top Priority
                      UnifiedAttendanceCard(
                        attendanceService: _automaticAttendanceService,
                        church: _cachedChurch, // Pass the cached church data
                        onTap: () {
                          // Optional: Show attendance details or settings
                        },
                      ),
                      const SizedBox(height: 24),

                      // Live Distance Tracking
                      LiveDistanceWidget(
                        autoStart: true,
                        showWalkingTime: true,
                        church: _cachedChurch, // Pass the cached church data
                        attendanceService:
                            _automaticAttendanceService, // Pass the same instance
                      ),
                      const SizedBox(height: 24),

                      // Church Information Card
                      _buildChurchInfoCard(user),
                      const SizedBox(height: 32),

                      // Videos Card
                      VideosCard(onTap: widget.onNavigateToVideos),
                      const SizedBox(height: 32),

                      RecentAttendanceCard(
                        onViewAllPressed: () {},
                        onRefresh: _onRefresh,
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ), // Added missing closing parenthesis for SingleChildScrollView
              ), // Closing parenthesis for RefreshIndicator child
            ), // Closing parenthesis for SafeArea child
          ), // Closing parenthesis for Container child
        ); // Closing parenthesis for Scaffold body
      },
    );
  }

  Widget _buildChurchInfoCard(user) {
    // Use cached church data if available, otherwise show loading
    final church = _cachedChurch;

    return Builder(
      builder: (context) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.church, color: AppColors.textPrimary, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'Church Information',
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (_isRefreshing)
                const Center(child: CircularProgressIndicator())
              else if (church != null) ...[
                _buildInfoRow('Name', church.name),
                if (church.branch != null && church.branch!.isNotEmpty)
                  _buildInfoRow('Branch', church.branch!),
                _buildInfoRow('Address', church.address),
                if (church.latitude != null && church.longitude != null) ...[
                  _buildInfoRow(
                    'Coordinates',
                    '${church.latitude!.toStringAsFixed(6)}, ${church.longitude!.toStringAsFixed(6)}',
                  ),
                  _buildInfoRow('Geofence Radius', '${church.geofenceRadius}m'),
                ],
              ] else
                Column(
                  children: [
                    Text(
                      'No church data available',
                      style: TextStyle(
                        color: AppColors.textPrimary.withValues(alpha: 0.7),
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: _onRefresh,
                      child: const Text('Refresh'),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: AppColors.textPrimary.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
