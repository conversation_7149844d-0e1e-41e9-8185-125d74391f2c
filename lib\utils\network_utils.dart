import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flockin_mobile/utils/constants.dart';

class NetworkUtils {
  static final Dio _dio = Dio()
    ..options.connectTimeout = const Duration(seconds: 5)
    ..options.receiveTimeout = const Duration(seconds: 5);

  /// Test if the backend API is reachable
  static Future<NetworkTestResult> testBackendConnection() async {
    try {
      debugPrint('Testing connection to: ${AppConstants.baseUrl}');

      // Test basic connectivity
      final response = await _dio.get(
        '${AppConstants.baseUrl}/health',
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      if (response.statusCode == 200) {
        debugPrint('✅ Backend connection successful');
        return NetworkTestResult(
          isConnected: true,
          message: 'Backend is reachable',
          statusCode: response.statusCode,
        );
      } else {
        debugPrint('⚠️ Backend responded with status: ${response.statusCode}');
        return NetworkTestResult(
          isConnected: false,
          message: 'Backend responded with status: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      String message;
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
          message = 'Connection timeout - backend may be down';
          break;
        case DioExceptionType.connectionError:
          message =
              'Connection error - check if backend is running on ${AppConstants.baseUrl}';
          break;
        default:
          message = 'Network error: ${e.message}';
      }

      debugPrint('❌ Backend connection failed: $message');
      return NetworkTestResult(
        isConnected: false,
        message: message,
        error: e.toString(),
      );
    } catch (e) {
      debugPrint('❌ Unexpected error testing backend: $e');
      return NetworkTestResult(
        isConnected: false,
        message: 'Unexpected error: $e',
        error: e.toString(),
      );
    }
  }

  /// Test internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Get network info for debugging
  static Future<Map<String, dynamic>> getNetworkInfo() async {
    final hasInternet = await hasInternetConnection();
    final backendTest = await testBackendConnection();

    return {
      'hasInternet': hasInternet,
      'backendReachable': backendTest.isConnected,
      'backendUrl': AppConstants.baseUrl,
      'backendMessage': backendTest.message,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

class NetworkTestResult {
  final bool isConnected;
  final String message;
  final int? statusCode;
  final String? error;

  NetworkTestResult({
    required this.isConnected,
    required this.message,
    this.statusCode,
    this.error,
  });

  @override
  String toString() {
    return 'NetworkTestResult(isConnected: $isConnected, message: $message, statusCode: $statusCode)';
  }
}
