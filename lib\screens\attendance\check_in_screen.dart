import 'package:flockin_mobile/services/attendance_service.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';
import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/utils/constants.dart';
import 'package:flutter/material.dart';

class CheckInScreen extends StatefulWidget {
  const CheckInScreen({super.key});

  @override
  State<CheckInScreen> createState() => _CheckInScreenState();
}

class _CheckInScreenState extends State<CheckInScreen> {
  final AttendanceService _attendanceService = AttendanceService();
  bool _isLoading = false;
  bool _isValidatingLocation = false;
  LocationData? _currentLocation;
  LocationValidationResult? _validationResult;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkLocationPermissions();
  }

  @override
  void dispose() {
    _attendanceService.dispose();
    super.dispose();
  }

  Future<void> _checkLocationPermissions() async {
    final hasPermissions = await _attendanceService.hasLocationPermissions();
    if (!hasPermissions) {
      _showPermissionDialog();
    } else {
      _getCurrentLocation();
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final location = await _attendanceService.getCurrentLocation();
      setState(() {
        _currentLocation = location;
        _isLoading = false;
      });

      if (location != null) {
        _validateLocation();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to get location: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _validateLocation() async {
    if (_currentLocation == null) return;

    setState(() {
      _isValidatingLocation = true;
    });

    try {
      final result = await _attendanceService.validateLocationForCheckIn();
      setState(() {
        _validationResult = result;
        _isValidatingLocation = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Location validation failed: ${e.toString()}';
        _isValidatingLocation = false;
      });
    }
  }

  Future<void> _performCheckIn() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _attendanceService.checkIn();
      
      if (result != null) {
        _showSuccessDialog(result);
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Location Permission Required'),
        content: const Text(
          'This app needs location permission to verify your attendance at church services. Please grant location permission to continue.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final granted = await _attendanceService.requestLocationPermissions();
              if (granted) {
                _getCurrentLocation();
              } else {
                _attendanceService.openLocationSettings();
              }
            },
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(CheckInResponse response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Check-in Successful!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(response.message),
            const SizedBox(height: 16),
            Text('Time: ${response.attendance.checkInTime.toString()}'),
            if (response.attendance.distance != null)
              Text('Distance: ${_attendanceService.formatDistance(response.attendance.distance!)}'),
            if (response.attendance.accuracy != null)
              Text('GPS Accuracy: ${response.attendance.accuracy!.toStringAsFixed(1)}m'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Check In'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Location Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: _currentLocation != null ? Colors.green : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Location Status',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Getting location...'),
                        ],
                      )
                    else if (_currentLocation != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Latitude: ${_currentLocation!.latitude.toStringAsFixed(6)}'),
                          Text('Longitude: ${_currentLocation!.longitude.toStringAsFixed(6)}'),
                          Text('Accuracy: ${_currentLocation!.accuracy.toStringAsFixed(1)}m'),
                          Text('Updated: ${_currentLocation!.timestamp.toString()}'),
                        ],
                      )
                    else
                      const Text('Location not available'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Validation Status Card
            if (_validationResult != null || _isValidatingLocation)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _validationResult?.canCheckIn == true 
                                ? Icons.check_circle 
                                : Icons.error,
                            color: _validationResult?.canCheckIn == true 
                                ? Colors.green 
                                : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Check-in Validation',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      if (_isValidatingLocation)
                        const Row(
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 8),
                            Text('Validating location...'),
                          ],
                        )
                      else if (_validationResult != null)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(_validationResult!.message),
                            Text('Distance: ${_attendanceService.formatDistance(_validationResult!.distance)}'),
                            if (_validationResult!.suspicionScore > 0)
                              Text('Suspicion Score: ${_validationResult!.suspicionScore.toStringAsFixed(1)}'),
                          ],
                        ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Error Message
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),

            const Spacer(),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _getCurrentLocation,
                    child: const Text('Refresh Location'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: (_validationResult?.canCheckIn == true && !_isLoading)
                        ? _performCheckIn
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Check In'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
