import 'package:flutter/material.dart';

import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/utils/constants.dart';
import 'package:flockin_mobile/widgets/common/gradient_container.dart';
import 'package:flockin_mobile/models/church_video.dart';
import 'package:flockin_mobile/screens/videos/video_player_screen.dart';

class ChurchVideosScreen extends StatefulWidget {
  const ChurchVideosScreen({super.key});

  @override
  State<ChurchVideosScreen> createState() => _ChurchVideosScreenState();
}

class _ChurchVideosScreenState extends State<ChurchVideosScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Real YouTube videos for testing
  final List<ChurchVideo> _featuredVideos = [
    ChurchVideo(
      id: 'JqG8Axo2iPM',
      title: 'God Will Work It In! | Pastor <PERSON>',
      description:
          'Sometimes, even when your situation isn\'t getting better, you are. Stop stressing like you need to do it alone.',
      thumbnailUrl: 'https://img.youtube.com/vi/JqG8Axo2iPM/hqdefault.jpg',
      duration: '42:15',
      uploadDate: DateTime.now().subtract(const Duration(days: 2)),
      category: 'Sunday Service',
      views: 125000,
    ),
    ChurchVideo(
      id: 'Yf6C0L_7-CA',
      title: 'How Great Thou Art | Carrie Underwood',
      description:
          'Official performance video of the beloved hymn "How Great Thou Art" by Carrie Underwood.',
      thumbnailUrl: 'https://img.youtube.com/vi/Yf6C0L_7-CA/hqdefault.jpg',
      duration: '4:32',
      uploadDate: DateTime.now().subtract(const Duration(days: 5)),
      category: 'Worship',
      views: 89000,
    ),
    ChurchVideo(
      id: 'fcYwXZYSApw',
      title: 'From Trouble To Double | Joel Osteen',
      description:
          'Inspirational message about how God can turn your troubles into blessings and double your joy.',
      thumbnailUrl: 'https://img.youtube.com/vi/fcYwXZYSApw/hqdefault.jpg',
      duration: '28:45',
      uploadDate: DateTime.now().subtract(const Duration(days: 7)),
      category: 'Sunday Service',
      views: 67000,
    ),
    ChurchVideo(
      id: 'CqybaIesbuA',
      title: 'Reckless Love | Cory Asbury',
      description:
          'Official music video of the beloved worship song "Reckless Love" about God\'s overwhelming love.',
      thumbnailUrl: 'https://img.youtube.com/vi/CqybaIesbuA/hqdefault.jpg',
      duration: '5:18',
      uploadDate: DateTime.now().subtract(const Duration(days: 9)),
      category: 'Worship',
      views: 298000,
    ),
  ];

  final List<ChurchVideo> _recentVideos = [
    ChurchVideo(
      id: 'XFRjr_x-yxU',
      title: 'This Is Amazing Grace | Phil Wickham',
      description:
          'Official music video of the powerful worship song "This Is Amazing Grace".',
      thumbnailUrl: 'https://img.youtube.com/vi/XFRjr_x-yxU/hqdefault.jpg',
      duration: '4:18',
      uploadDate: DateTime.now().subtract(const Duration(days: 1)),
      category: 'Worship',
      views: 45600,
    ),
    ChurchVideo(
      id: 'Nb24TllMq9E',
      title: 'Keep The Faith | Pastor Sarah Jakes Roberts',
      description:
          'Encouraging message about maintaining faith through life\'s challenges and trials.',
      thumbnailUrl: 'https://img.youtube.com/vi/Nb24TllMq9E/hqdefault.jpg',
      duration: '35:20',
      uploadDate: DateTime.now().subtract(const Duration(days: 3)),
      category: 'Sunday Service',
      views: 72300,
    ),
    ChurchVideo(
      id: 'xsNDxIBVEx8',
      title: '10,000 Reasons | Celtic Worship ft. Steph Macleod',
      description:
          'Beautiful Celtic arrangement of Matt Redman\'s beloved worship song "10,000 Reasons".',
      thumbnailUrl: 'https://img.youtube.com/vi/xsNDxIBVEx8/hqdefault.jpg',
      duration: '5:45',
      uploadDate: DateTime.now().subtract(const Duration(days: 4)),
      category: 'Worship',
      views: 34500,
    ),
    ChurchVideo(
      id: 'j7WT1FqCmYQ',
      title: 'Follow The Anointing Within | Joseph Prince',
      description:
          'Full sermon about following God\'s anointing and guidance in your daily life.',
      thumbnailUrl: 'https://img.youtube.com/vi/j7WT1FqCmYQ/hqdefault.jpg',
      duration: '52:30',
      uploadDate: DateTime.now().subtract(const Duration(days: 6)),
      category: 'Sunday Service',
      views: 89200,
    ),
    ChurchVideo(
      id: 'EueRjHaGbS4',
      title: 'How Great Thou Art | Shane & Shane',
      description:
          'Acoustic worship version of the classic hymn from the Hymns Live project.',
      thumbnailUrl: 'https://img.youtube.com/vi/EueRjHaGbS4/hqdefault.jpg',
      duration: '6:12',
      uploadDate: DateTime.now().subtract(const Duration(days: 8)),
      category: 'Worship',
      views: 28900,
    ),
    ChurchVideo(
      id: 'Obp-9BEZe1c',
      title: 'Amazing Grace (My Chains Are Gone) | Pentatonix',
      description:
          'Beautiful a cappella arrangement of Chris Tomlin\'s version of Amazing Grace.',
      thumbnailUrl: 'https://img.youtube.com/vi/Obp-9BEZe1c/hqdefault.jpg',
      duration: '4:55',
      uploadDate: DateTime.now().subtract(const Duration(days: 10)),
      category: 'Worship',
      views: 156000,
    ),
    ChurchVideo(
      id: 'SitsLDo6X_Y',
      title: 'Election 2024: How to Vote Like Jesus | Josh Howerton',
      description:
          'Relevant message for Christians about engaging in politics and voting with biblical principles.',
      thumbnailUrl: 'https://img.youtube.com/vi/SitsLDo6X_Y/hqdefault.jpg',
      duration: '38:15',
      uploadDate: DateTime.now().subtract(const Duration(days: 12)),
      category: 'Bible Study',
      views: 42100,
    ),
    ChurchVideo(
      id: 'kOiQgleiRtU',
      title: 'Goodness of God | Bethel Music',
      description:
          'Official music video of the powerful worship song "Goodness of God" by Bethel Music.',
      thumbnailUrl: 'https://img.youtube.com/vi/kOiQgleiRtU/hqdefault.jpg',
      duration: '6:43',
      uploadDate: DateTime.now().subtract(const Duration(days: 14)),
      category: 'Worship',
      views: 234000,
    ),
    ChurchVideo(
      id: 'GhYBaOpkNdI',
      title: 'Way Maker | Sinach',
      description:
          'Official video of the globally acclaimed worship song "Way Maker" by Sinach.',
      thumbnailUrl: 'https://img.youtube.com/vi/GhYBaOpkNdI/hqdefault.jpg',
      duration: '4:12',
      uploadDate: DateTime.now().subtract(const Duration(days: 16)),
      category: 'Worship',
      views: 189000,
    ),
    ChurchVideo(
      id: 'RvYiw0cvd40',
      title: 'The Power of Prayer | T.D. Jakes',
      description:
          'Powerful sermon about the importance and effectiveness of prayer in the Christian life.',
      thumbnailUrl: 'https://img.youtube.com/vi/RvYiw0cvd40/hqdefault.jpg',
      duration: '45:30',
      uploadDate: DateTime.now().subtract(const Duration(days: 18)),
      category: 'Sunday Service',
      views: 156000,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _playVideo(ChurchVideo video) {
    // Get related videos (exclude the current video)
    final allVideos = [..._featuredVideos, ..._recentVideos];
    final relatedVideos = allVideos.where((v) => v.id != video.id).toList();

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            VideoPlayerScreen(video: video, relatedVideos: relatedVideos),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Tab bar
              _buildTabBar(),

              // Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildFeaturedTab(),
                    _buildRecentTab(),
                    _buildCategoriesTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Church Videos',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  'Watch sermons, worship, and more',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              // Search functionality
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              ),
              child: const Icon(
                Icons.search,
                color: AppColors.textPrimary,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'Featured'),
          Tab(text: 'Recent'),
          Tab(text: 'Categories'),
        ],
      ),
    );
  }

  Widget _buildFeaturedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(
            'Featured Videos',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          ..._featuredVideos.map(
            (video) => _buildVideoCard(video, isLarge: true),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(
            'Recent Uploads',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          ..._recentVideos.map((video) => _buildVideoCard(video)),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    final categories = [
      {'name': 'Sunday Service', 'count': 45, 'icon': Icons.church},
      {'name': 'Bible Study', 'count': 32, 'icon': Icons.menu_book},
      {'name': 'Worship', 'count': 28, 'icon': Icons.music_note},
      {'name': 'Youth', 'count': 15, 'icon': Icons.groups},
      {'name': 'Prayer', 'count': 12, 'icon': Icons.favorite},
      {'name': 'Testimonies', 'count': 8, 'icon': Icons.record_voice_over},
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(
            'Browse by Category',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.3,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryCard(
                category['name'] as String,
                category['count'] as int,
                category['icon'] as IconData,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(ChurchVideo video, {bool isLarge = false}) {
    return GestureDetector(
      onTap: () => _playVideo(video),
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        child: GlassContainer(
          borderRadius: BorderRadius.circular(20),
          padding: EdgeInsets.zero,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                child: Stack(
                  children: [
                    Container(
                      height: isLarge ? 200 : 160,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: AppColors.primaryGradient,
                        image: DecorationImage(
                          image: NetworkImage(video.thumbnailUrl),
                          fit: BoxFit.cover,
                          onError: (exception, stackTrace) {
                            // Handle image loading error
                          },
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          video.duration,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Center(
                        child: Icon(
                          Icons.play_circle_filled,
                          color: Colors.white,
                          size: 60,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Video info
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: AppColors.primaryGradient,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            video.category,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.visibility,
                          color: AppColors.textMuted,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${video.views}',
                          style: TextStyle(
                            color: AppColors.textMuted,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      video.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      video.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _formatDate(video.uploadDate),
                      style: TextStyle(
                        color: AppColors.textMuted,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String name, int count, IconData icon) {
    return GestureDetector(
      onTap: () {
        // Navigate to category videos
      },
      child: GlassContainer(
        borderRadius: BorderRadius.circular(20),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              '$count videos',
              style: TextStyle(color: AppColors.textMuted, fontSize: 11),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
