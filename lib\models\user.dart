class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String memberId;
  final String churchId;
  final String churchName;
  final DateTime registrationDate;
  final String status;
  final String? profileImageUrl;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.memberId,
    required this.churchId,
    required this.churchName,
    required this.registrationDate,
    required this.status,
    this.profileImageUrl,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      memberId: json['member_id'] ?? '',
      churchId: json['church_id'] ?? '',
      churchName: json['church_name'] ?? '',
      registrationDate: DateTime.parse(json['registration_date'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'active',
      profileImageUrl: json['profile_image_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'member_id': memberId,
      'church_id': churchId,
      'church_name': churchName,
      'registration_date': registrationDate.toIso8601String(),
      'status': status,
      'profile_image_url': profileImageUrl,
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? memberId,
    String? churchId,
    String? churchName,
    DateTime? registrationDate,
    String? status,
    String? profileImageUrl,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      memberId: memberId ?? this.memberId,
      churchId: churchId ?? this.churchId,
      churchName: churchName ?? this.churchName,
      registrationDate: registrationDate ?? this.registrationDate,
      status: status ?? this.status,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  String get initials {
    List<String> nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts[0][0].toUpperCase();
    }
    return 'U';
  }

  @override
  String toString() {
    return 'User{id: $id, name: $name, memberId: $memberId, churchName: $churchName}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
