import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flockin_app/utils/constants.dart';

enum MemberRole { member, admin, pastor }

enum MemberStatus { active, inactive, suspended }

class Member {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String? phone;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? address;
  final String? profilePicture;
  final MemberRole role;
  final MemberStatus status;
  final String churchId;
  final DateTime? lastLoginAt;
  final bool isLocationTrackingEnabled;
  final bool isNotificationsEnabled;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String fullName;

  Member({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.profilePicture,
    required this.role,
    required this.status,
    required this.churchId,
    this.lastLoginAt,
    required this.isLocationTrackingEnabled,
    required this.isNotificationsEnabled,
    this.emergencyContactName,
    this.emergencyContactPhone,
    required this.createdAt,
    required this.updatedAt,
    required this.fullName,
  });

  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      id: json['id'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      email: json['email'],
      phone: json['phone'],
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'])
          : null,
      gender: json['gender'],
      address: json['address'],
      profilePicture: json['profilePicture'],
      role: _parseRole(json['role']),
      status: _parseStatus(json['status']),
      churchId: json['churchId'],
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'])
          : null,
      isLocationTrackingEnabled: json['isLocationTrackingEnabled'] ?? true,
      isNotificationsEnabled: json['isNotificationsEnabled'] ?? true,
      emergencyContactName: json['emergencyContactName'],
      emergencyContactPhone: json['emergencyContactPhone'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      fullName: json['fullName'],
    );
  }

  static MemberRole _parseRole(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return MemberRole.admin;
      case 'pastor':
        return MemberRole.pastor;
      default:
        return MemberRole.member;
    }
  }

  static MemberStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'inactive':
        return MemberStatus.inactive;
      case 'suspended':
        return MemberStatus.suspended;
      default:
        return MemberStatus.active;
    }
  }

  bool get isAdmin => role == MemberRole.admin || role == MemberRole.pastor;
  bool get isActive => status == MemberStatus.active;
}

class CreateMemberRequest {
  final String firstName;
  final String lastName;
  final String email;
  final String? phone;
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  final String googleId;
  final String? profilePicture;
  final String churchId;
  final String? emergencyContactName;
  final String? emergencyContactPhone;

  CreateMemberRequest({
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.address,
    required this.googleId,
    this.profilePicture,
    required this.churchId,
    this.emergencyContactName,
    this.emergencyContactPhone,
  });

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      if (phone != null) 'phone': phone,
      if (dateOfBirth != null) 'dateOfBirth': dateOfBirth,
      if (gender != null) 'gender': gender,
      if (address != null) 'address': address,
      'googleId': googleId,
      if (profilePicture != null) 'profilePicture': profilePicture,
      'churchId': churchId,
      if (emergencyContactName != null)
        'emergencyContactName': emergencyContactName,
      if (emergencyContactPhone != null)
        'emergencyContactPhone': emergencyContactPhone,
    };
  }
}

class UpdateMemberRequest {
  final String? firstName;
  final String? lastName;
  final String? phone;
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  final String? profilePicture;
  final bool? isLocationTrackingEnabled;
  final bool? isNotificationsEnabled;
  final String? emergencyContactName;
  final String? emergencyContactPhone;

  UpdateMemberRequest({
    this.firstName,
    this.lastName,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.profilePicture,
    this.isLocationTrackingEnabled,
    this.isNotificationsEnabled,
    this.emergencyContactName,
    this.emergencyContactPhone,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (firstName != null) data['firstName'] = firstName;
    if (lastName != null) data['lastName'] = lastName;
    if (phone != null) data['phone'] = phone;
    if (dateOfBirth != null) data['dateOfBirth'] = dateOfBirth;
    if (gender != null) data['gender'] = gender;
    if (address != null) data['address'] = address;
    if (profilePicture != null) data['profilePicture'] = profilePicture;
    if (isLocationTrackingEnabled != null) {
      data['isLocationTrackingEnabled'] = isLocationTrackingEnabled;
    }
    if (isNotificationsEnabled != null) {
      data['isNotificationsEnabled'] = isNotificationsEnabled;
    }
    if (emergencyContactName != null) {
      data['emergencyContactName'] = emergencyContactName;
    }
    if (emergencyContactPhone != null) {
      data['emergencyContactPhone'] = emergencyContactPhone;
    }
    return data;
  }
}

class MemberService {
  /// Get authentication token from storage
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  /// Get common headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Create a new member (registration)
  Future<Member?> createMember(CreateMemberRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/members'),
        headers: await _getHeaders(),
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return Member.fromJson(data['member']);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Member creation failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Create member error: $e');
      }
      rethrow;
    }
  }

  /// Get current user profile
  Future<Member?> getMyProfile() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/members/me'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        return Member.fromJson(jsonDecode(response.body));
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get profile');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get profile error: $e');
      }
      rethrow;
    }
  }

  /// Update current user profile
  Future<Member?> updateMyProfile(UpdateMemberRequest request) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConstants.baseUrl}/members/me'),
        headers: await _getHeaders(),
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Member.fromJson(data['member']);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Profile update failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Update profile error: $e');
      }
      rethrow;
    }
  }

  /// Join a different church
  Future<Member?> joinChurch(String churchId) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/members/join-church'),
        headers: await _getHeaders(),
        body: jsonEncode({'churchId': churchId}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return Member.fromJson(data['member']);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to join church');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Join church error: $e');
      }
      rethrow;
    }
  }

  /// Get all members of a church
  Future<List<Member>> getChurchMembers(
    String churchId, {
    bool includeInactive = false,
  }) async {
    try {
      final uri = Uri.parse('${AppConstants.baseUrl}/members/church/$churchId')
          .replace(
            queryParameters: includeInactive
                ? {'includeInactive': 'true'}
                : null,
          );

      final response = await http.get(uri, headers: await _getHeaders());

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Member.fromJson(json)).toList();
      } else {
        throw Exception('Failed to get church members');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get church members error: $e');
      }
      rethrow;
    }
  }

  /// Search members by name or email
  Future<List<Member>> searchMembers(String churchId, String query) async {
    try {
      if (query.trim().length < 2) {
        return [];
      }

      final response = await http.get(
        Uri.parse(
          '${AppConstants.baseUrl}/members/church/$churchId/search?q=${Uri.encodeComponent(query)}',
        ),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Member.fromJson(json)).toList();
      } else {
        throw Exception('Failed to search members');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Search members error: $e');
      }
      rethrow;
    }
  }

  /// Get member statistics for a church
  Future<Map<String, dynamic>> getChurchMemberStats(String churchId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/members/church/$churchId/stats'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get member statistics');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get member stats error: $e');
      }
      rethrow;
    }
  }
}
