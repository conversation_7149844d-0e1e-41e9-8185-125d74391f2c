import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

class LocationData {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      accuracy: json['accuracy']?.toDouble() ?? 0.0,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class GeofenceValidationResult {
  final bool isWithinGeofence;
  final double distance;
  final String message;
  final double? accuracy;

  GeofenceValidationResult({
    required this.isWithinGeofence,
    required this.distance,
    required this.message,
    this.accuracy,
  });
}

class GeolocationService {
  static const double _earthRadiusMeters = 6371000;
  static const double _defaultMaxAccuracy = 50; // meters
  static const double _defaultMinAccuracy = 5; // meters

  StreamSubscription<Position>? _positionStreamSubscription;
  Position? _lastKnownPosition;

  /// Check and request location permissions
  Future<bool> checkAndRequestPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('Location services are disabled.');
        }
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) {
            print('Location permissions are denied');
          }
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('Location permissions are permanently denied');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking permissions: $e');
      }
      return false;
    }
  }

  /// Get current location with high accuracy
  Future<LocationData?> getCurrentLocation({
    Duration timeout = const Duration(seconds: 15),
  }) async {
    try {
      bool hasPermission = await checkAndRequestPermissions();
      if (!hasPermission) {
        return null;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: timeout,
      );

      _lastKnownPosition = position;

      final locationData = LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: position.timestamp,
      );

      if (kDebugMode) {
        print(
          '📍 Current location retrieved: ${locationData.latitude}, ${locationData.longitude} (accuracy: ${locationData.accuracy}m)',
        );
      }

      return locationData;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current location: $e');
      }
      return null;
    }
  }

  /// Get last known location (faster but potentially less accurate)
  Future<LocationData?> getLastKnownLocation() async {
    try {
      bool hasPermission = await checkAndRequestPermissions();
      if (!hasPermission) {
        return null;
      }

      Position? position = await Geolocator.getLastKnownPosition();

      if (position != null) {
        _lastKnownPosition = position;
        return LocationData(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          timestamp: position.timestamp,
        );
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting last known location: $e');
      }
      return null;
    }
  }

  /// Start listening to location updates
  StreamSubscription<Position>? startLocationUpdates({
    required Function(LocationData) onLocationUpdate,
    Function(String)? onError,
    int intervalMs = 5000, // 5 seconds
  }) {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update when moved 10 meters
    );

    _positionStreamSubscription =
        Geolocator.getPositionStream(locationSettings: locationSettings).listen(
          (Position position) {
            _lastKnownPosition = position;
            onLocationUpdate(
              LocationData(
                latitude: position.latitude,
                longitude: position.longitude,
                accuracy: position.accuracy,
                timestamp: position.timestamp,
              ),
            );
          },
          onError: (error) {
            if (kDebugMode) {
              print('Location stream error: $error');
            }
            onError?.call(error.toString());
          },
        );

    return _positionStreamSubscription;
  }

  /// Stop location updates
  void stopLocationUpdates() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
  }

  /// Calculate distance between two coordinates using Haversine formula
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    double dLat = _toRadians(lat2 - lat1);
    double dLon = _toRadians(lon2 - lon1);

    double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRadians(lat1)) *
            cos(_toRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return _earthRadiusMeters * c;
  }

  /// Validate if location is within geofence
  GeofenceValidationResult validateGeofence({
    required double userLat,
    required double userLon,
    required double centerLat,
    required double centerLon,
    required double radiusMeters,
    double? accuracy,
  }) {
    double distance = calculateDistance(userLat, userLon, centerLat, centerLon);
    bool isWithinGeofence = distance <= radiusMeters;

    String message;
    if (isWithinGeofence) {
      message = 'You are within the check-in area';
    } else {
      message = 'You are ${formatDistance(distance)} away from the church';
    }

    // Consider GPS accuracy in validation
    if (accuracy != null) {
      if (accuracy > _defaultMaxAccuracy) {
        isWithinGeofence = false;
        message = 'GPS accuracy is too poor for check-in';
      } else if (accuracy <= _defaultMinAccuracy &&
          distance <= (radiusMeters + accuracy)) {
        isWithinGeofence = true;
        message =
            'You are within the check-in area (with GPS accuracy considered)';
      }
    }

    return GeofenceValidationResult(
      isWithinGeofence: isWithinGeofence,
      distance: distance,
      message: message,
      accuracy: accuracy,
    );
  }

  /// Format distance for human display
  String formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m';
    } else {
      return '${(distanceMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  /// Check if coordinates are valid
  bool isValidCoordinates(double latitude, double longitude) {
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180 &&
        !latitude.isNaN &&
        !longitude.isNaN;
  }

  /// Get location permission status
  Future<LocationPermission> getPermissionStatus() async {
    return await Geolocator.checkPermission();
  }

  /// Open app settings for location permissions
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  /// Open app settings for app permissions
  Future<void> openAppSettings() async {
    await openAppSettings();
  }

  /// Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Get cached last known position
  Position? get lastKnownPosition => _lastKnownPosition;

  /// Convert degrees to radians
  double _toRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Dispose resources
  void dispose() {
    stopLocationUpdates();
  }
}
