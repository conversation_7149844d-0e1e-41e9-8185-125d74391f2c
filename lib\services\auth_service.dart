import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'package:flockin_app/models/user_model.dart';
import 'package:flockin_app/utils/constants.dart';

class AuthService extends ChangeNotifier {
  static const _storage = FlutterSecureStorage();
  static const _tokenKey = 'auth_token';
  static const _refreshTokenKey = 'refresh_token';
  static const _userDataKey = 'user_data';

  final Dio _dio = Dio();

  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  AuthService() {
    _initializeDio();
    _checkAuthStatus();
  }

  void _initializeDio() {
    _dio.options.baseUrl = AppConstants.baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);

    // Add auth interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _storage.read(key: _tokenKey);
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token expired, try to refresh
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry the original request
              final token = await _storage.read(key: _tokenKey);
              error.requestOptions.headers['Authorization'] = 'Bearer $token';
              final response = await _dio.fetch(error.requestOptions);
              handler.resolve(response);
              return;
            } else {
              // Refresh failed, logout user
              await logout();
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  Future<void> _checkAuthStatus() async {
    _setLoading(true);
    try {
      final token = await _storage.read(key: _tokenKey);
      final userData = await _storage.read(key: _userDataKey);

      if (token != null && userData != null) {
        _currentUser = User.fromJson(jsonDecode(userData));
        _isAuthenticated = true;
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
      await logout();
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signInWithEmailPassword(String email, String password) async {
    _setLoading(true);
    try {
      debugPrint(
        'Attempting login to: ${_dio.options.baseUrl}${ApiEndpoints.login}',
      );
      debugPrint('Login email: $email');

      final response = await _dio.post(
        ApiEndpoints.login,
        data: {'identifier': email, 'password': password},
      );

      debugPrint('Login response status: ${response.statusCode}');
      debugPrint('Login response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;

        // Store tokens and user data
        await _storage.write(key: _tokenKey, value: data['accessToken']);
        await _storage.write(
          key: _refreshTokenKey,
          value: data['refreshToken'],
        );
        await _storage.write(
          key: _userDataKey,
          value: jsonEncode(data['user']),
        );

        // Update state
        _currentUser = User.fromJson(data['user']);
        _isAuthenticated = true;
        notifyListeners();

        debugPrint('✅ Login successful for user: ${_currentUser?.email}');
        return true;
      }
      debugPrint('❌ Login failed with status: ${response.statusCode}');
      return false;
    } on DioException catch (e) {
      debugPrint('❌ Dio error during login: ${e.type}');
      debugPrint('Error message: ${e.message}');
      debugPrint('Response data: ${e.response?.data}');
      debugPrint('Status code: ${e.response?.statusCode}');

      if (e.type == DioExceptionType.connectionError) {
        debugPrint('🔗 Connection error - check if backend is running');
      } else if (e.type == DioExceptionType.connectionTimeout) {
        debugPrint('⏱️ Connection timeout - backend may be slow or down');
      }

      return false;
    } catch (e) {
      debugPrint('❌ Unexpected error during login: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storage.read(key: _refreshTokenKey);
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
      );

      if (response.statusCode == 200) {
        final newToken = response.data['accessToken'];
        await _storage.write(key: _tokenKey, value: newToken);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Token refresh error: $e');
      return false;
    }
  }

  Future<bool> checkAuthStatus() async {
    try {
      // Check if we have stored tokens
      final token = await _storage.read(key: _tokenKey);
      final userData = await _storage.read(key: _userDataKey);

      if (token != null && userData != null) {
        // Restore user data
        _currentUser = User.fromJson(jsonDecode(userData));
        _isAuthenticated = true;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Auth status check error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      // Clear stored data
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _refreshTokenKey);
      await _storage.delete(key: _userDataKey);

      // Update state
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> forgotPassword(String email) async {
    try {
      _setLoading(true);

      final response = await _dio.post(
        '/auth/forgot-password',
        data: {'email': email},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Forgot password error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
