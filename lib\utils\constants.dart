import 'package:flockin_mobile/config/environment.dart';

class AppConstants {
  // App Info
  static String get appName => EnvironmentConfig.appName;
  static const String appVersion = '1.0.0';

  // API Configuration - Now environment-aware
  static String get baseUrl => EnvironmentConfig.baseUrl;
  static String get apiVersion => EnvironmentConfig.apiVersion;

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String churchDataKey = 'church_data';
  static const String attendanceHistoryKey = 'attendance_history';

  // Location Settings
  static const double geofenceRadius = 100.0; // meters
  static const int locationUpdateInterval = 30; // seconds
  static const double locationAccuracyThreshold = 50.0; // meters

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // Attendance Status
  static const String statusPresent = 'present';
  static const String statusAbsent = 'absent';
  static const String statusLate = 'late';

  // Service Types
  static const String sundayService = 'Sunday Service';
  static const String midweekService = 'Midweek Service';
  static const String specialService = 'Special Service';

  // Error Messages
  static const String networkError =
      'Network connection error. Please check your internet connection.';
  static const String locationError =
      'Unable to get your location. Please enable location services.';
  static const String authError = 'Authentication failed. Please try again.';
  static const String generalError = 'Something went wrong. Please try again.';

  // Success Messages
  static const String checkInSuccess = 'Successfully checked in!';
  static const String checkOutSuccess = 'Successfully checked out!';
  static const String registrationSuccess = 'Registration successful!';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const String phoneRegex = r'^\+233[0-9]{9}$'; // Ghana phone format

  // Date Formats
  static const String dateFormat = 'MMM dd, yyyy';
  static const String timeFormat = 'hh:mm a';
  static const String dateTimeFormat = 'MMM dd, yyyy hh:mm a';

  // Permissions
  static const List<String> requiredPermissions = [
    'location',
    'location_always',
    'notification',
  ];
}

class ApiEndpoints {
  static const String auth = '/auth';
  static const String register = '$auth/register';
  static const String login = '$auth/login';
  static const String verify = '$auth/verify';
  static const String refresh = '$auth/refresh';

  static const String churches = '/churches';
  static const String attendance = '/attendance';
  static const String checkIn = '$attendance/checkin';
  static const String checkOut = '$attendance/checkout';
  static const String history = '$attendance/history';
  static const String live = '$attendance/live';
  static const String reports = '$attendance/reports';

  static const String user = '/user';
  static const String profile = '$user/profile';
  static const String updateProfile = '$user/update';
}
