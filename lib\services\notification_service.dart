import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flockin_app/utils/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Top-level function for background message handling
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('Handling a background message: ${message.messageId}');
    print('Message data: ${message.data}');
    print('Message notification: ${message.notification?.title}');
  }
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final Dio _dio = Dio();
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  static const String _fcmTokenKey = 'fcm_token';
  static const String _notificationChannelId = 'church_notifications';
  static const String _notificationChannelName = 'Church Notifications';
  static const String _notificationChannelDescription =
      'Notifications for church services and updates';

  /// Initialize the notification service
  static Future<void> initialize() async {
    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();

      // Request permissions
      await _requestPermissions();

      // Get and store FCM token
      await _getFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      if (kDebugMode) {
        print('✅ Notification service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing notification service: $e');
      }
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        _notificationChannelId,
        _notificationChannelName,
        description: _notificationChannelDescription,
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);
    }
  }

  /// Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Set background message handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    // Request Firebase messaging permissions
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (kDebugMode) {
      print('User granted permission: ${settings.authorizationStatus}');
    }

    // Request local notification permissions for iOS
    if (Platform.isIOS) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin
          >()
          ?.requestPermissions(alert: true, badge: true, sound: true);
    }
  }

  /// Get and store FCM token
  static Future<String?> _getFCMToken() async {
    try {
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _storeFCMToken(token);
        await _sendTokenToServer(token);
        if (kDebugMode) {
          print('FCM Token: $token');
        }
      }
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Store FCM token locally
  static Future<void> _storeFCMToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_fcmTokenKey, token);
  }

  /// Get stored FCM token
  static Future<String?> getStoredFCMToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_fcmTokenKey);
  }

  /// Send FCM token to server
  static Future<void> _sendTokenToServer(String token) async {
    try {
      // Initialize Dio with base URL and auth token
      _dio.options.baseUrl = AppConstants.baseUrl;
      final authToken = await _storage.read(key: 'auth_token');
      if (authToken != null) {
        _dio.options.headers['Authorization'] = 'Bearer $authToken';
      }

      await _dio.post('/members/fcm-token', data: {'fcmToken': token});
      if (kDebugMode) {
        print('✅ FCM token sent to server successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending FCM token to server: $e');
      }
    }
  }

  /// Set up message handlers
  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _handleInitialMessage();

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      _storeFCMToken(newToken);
      _sendTokenToServer(newToken);
      if (kDebugMode) {
        print('FCM token refreshed: $newToken');
      }
    });
  }

  /// Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('Received foreground message: ${message.messageId}');
      print('Message data: ${message.data}');
      print('Message notification: ${message.notification?.title}');
    }

    // Show local notification when app is in foreground
    await _showLocalNotification(message);
  }

  /// Handle notification tap
  static Future<void> _handleNotificationTap(RemoteMessage message) async {
    if (kDebugMode) {
      print('Notification tapped: ${message.messageId}');
      print('Message data: ${message.data}');
    }

    // Handle navigation based on notification data
    _handleNotificationNavigation(message.data);
  }

  /// Handle initial message when app is opened from terminated state
  static Future<void> _handleInitialMessage() async {
    RemoteMessage? initialMessage = await _firebaseMessaging
        .getInitialMessage();
    if (initialMessage != null) {
      if (kDebugMode) {
        print('App opened from notification: ${initialMessage.messageId}');
      }
      _handleNotificationNavigation(initialMessage.data);
    }
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          _notificationChannelId,
          _notificationChannelName,
          channelDescription: _notificationChannelDescription,
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
          enableVibration: true,
          playSound: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Church Notification',
      message.notification?.body ?? 'You have a new notification',
      platformChannelSpecifics,
      payload: jsonEncode(message.data),
    );
  }

  /// Handle notification tap from local notifications
  static void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        _handleNotificationNavigation(data);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing notification payload: $e');
        }
      }
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(Map<String, dynamic> data) {
    final notificationType = data['type'];

    if (kDebugMode) {
      print('Handling notification navigation for type: $notificationType');
      print('Notification data: $data');
    }

    // For now, we'll just log the navigation intent
    // In a full implementation, you would use a navigation service
    // or global navigator key to navigate to specific screens

    switch (notificationType) {
      case 'service_schedule':
        if (kDebugMode) {
          print(
            '📅 Service schedule notification - would navigate to dashboard/services',
          );
        }
        // TODO: Navigate to dashboard or services screen
        // Example: NavigationService.navigateTo('/dashboard');
        break;

      case 'attendance_reminder':
        if (kDebugMode) {
          print(
            '📍 Attendance reminder - would navigate to attendance/location screen',
          );
        }
        // TODO: Navigate to attendance tracking screen
        // Example: NavigationService.navigateTo('/attendance');
        break;

      case 'church_announcement':
        if (kDebugMode) {
          print('📢 Church announcement - would navigate to announcements');
        }
        // TODO: Navigate to announcements or notifications screen
        // Example: NavigationService.navigateTo('/notifications');
        break;

      default:
        if (kDebugMode) {
          print(
            '🔔 General notification - would navigate to notifications screen',
          );
        }
        // TODO: Navigate to general notifications screen
        // Example: NavigationService.navigateTo('/notifications');
        break;
    }

    // For now, we'll store the navigation intent for the app to handle
    // when it becomes active
    _storeNavigationIntent(notificationType, data);
  }

  /// Store navigation intent for later handling
  static Future<void> _storeNavigationIntent(
    String type,
    Map<String, dynamic> data,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_navigation_type', type);
      await prefs.setString('pending_navigation_data', jsonEncode(data));

      if (kDebugMode) {
        print('Stored navigation intent: $type');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error storing navigation intent: $e');
      }
    }
  }

  /// Get and clear pending navigation intent
  static Future<Map<String, dynamic>?> getPendingNavigationIntent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final type = prefs.getString('pending_navigation_type');
      final dataString = prefs.getString('pending_navigation_data');

      if (type != null && dataString != null) {
        // Clear the stored intent
        await prefs.remove('pending_navigation_type');
        await prefs.remove('pending_navigation_data');

        return {'type': type, 'data': jsonDecode(dataString)};
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting navigation intent: $e');
      }
    }
    return null;
  }

  /// Get current FCM token
  static Future<String?> getCurrentToken() async {
    return await _firebaseMessaging.getToken();
  }

  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      if (kDebugMode) {
        print('✅ Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error subscribing to topic $topic: $e');
      }
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      if (kDebugMode) {
        print('✅ Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error unsubscribing from topic $topic: $e');
      }
    }
  }
}
