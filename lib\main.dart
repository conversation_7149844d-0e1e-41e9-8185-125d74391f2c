import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';

import 'firebase_options.dart';
import 'screens/initialization/app_initialization_screen.dart';
import 'test_firebase_connection.dart';
import 'services/firebase_auth_service.dart';
import 'services/notification_service.dart';
import 'utils/colors.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with platform-specific options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize notification service
    await NotificationService.initialize();

    runApp(const FlockinApp());
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing app: $e');
    }
    runApp(const FlockinApp());
  }
}

class FlockinApp extends StatelessWidget {
  const FlockinApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => FirebaseAuthService())],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.darkTheme,
        home: const AppInitializationScreen(), // Back to main app
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
