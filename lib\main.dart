import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';

import 'package:flockin_mobile/screens/initialization/app_initialization_screen.dart';
import 'package:flockin_mobile/services/auth_service.dart';
import 'package:flockin_mobile/services/notification_service.dart';
import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize notification service
    await NotificationService.initialize();

    runApp(const FlockinApp());
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing app: $e');
    }
    runApp(const FlockinApp());
  }
}

class FlockinApp extends StatelessWidget {
  const FlockinApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => AuthService())],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.darkTheme,
        home: const AppInitializationScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
