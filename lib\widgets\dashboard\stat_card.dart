import 'package:flutter/material.dart';

import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/widgets/common/gradient_container.dart';

class StatCard extends StatefulWidget {
  final String value;
  final String label;
  final LinearGradient gradient;
  final IconData icon;
  final Color iconColor;

  const StatCard({
    super.key,
    required this.value,
    required this.label,
    required this.gradient,
    required this.icon,
    required this.iconColor,
  });

  @override
  State<StatCard> createState() => _StatCardState();
}

class _StatCardState extends State<StatCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    // Start animation after a short delay
    Future.delayed(
      Duration(milliseconds: 200 * (widget.value.hashCode % 4)),
      () {
        if (mounted) {
          _controller.forward();
        }
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: AnimatedGradientContainer(
              gradient: widget.gradient,
              borderRadius: BorderRadius.circular(20),
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(widget.icon, color: widget.iconColor, size: 24),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.value,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.w900,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.label,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class AttendanceStatsGrid extends StatelessWidget {
  final int servicesAttended;
  final String attendanceRate;
  final int currentStreak;
  final int totalServices;

  const AttendanceStatsGrid({
    super.key,
    required this.servicesAttended,
    required this.attendanceRate,
    required this.currentStreak,
    required this.totalServices,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: StatCard(
                value: '$servicesAttended',
                label: 'Services\nAttended',
                gradient: AppColors.servicesAttendedGradient,
                icon: Icons.event_available,
                iconColor: Colors.white,
              ),
            ),
            Expanded(
              child: StatCard(
                value: attendanceRate,
                label: 'Attendance\nRate',
                gradient: AppColors.attendanceRateGradient,
                icon: Icons.trending_up,
                iconColor: Colors.white,
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: StatCard(
                value: '$currentStreak',
                label: 'Current\nStreak',
                gradient: AppColors.currentStreakGradient,
                icon: Icons.local_fire_department,
                iconColor: Colors.white,
              ),
            ),
            Expanded(
              child: StatCard(
                value: '$totalServices',
                label: 'Total\nServices',
                gradient: AppColors.totalServicesGradient,
                icon: Icons.calendar_month,
                iconColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class PulsingIcon extends StatefulWidget {
  final IconData icon;
  final Color color;
  final double size;

  const PulsingIcon({
    super.key,
    required this.icon,
    required this.color,
    this.size = 24,
  });

  @override
  State<PulsingIcon> createState() => _PulsingIconState();
}

class _PulsingIconState extends State<PulsingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Icon(widget.icon, color: widget.color, size: widget.size),
        );
      },
    );
  }
}
