import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class Church {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String? description;
  final String? imageUrl;
  final List<String> serviceTypes;
  final Map<String, dynamic>? serviceTimes;

  Church({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.description,
    this.imageUrl,
    this.serviceTypes = const [],
    this.serviceTimes,
  });

  factory Church.fromJson(Map<String, dynamic> json) {
    return Church(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      description: json['description'],
      imageUrl: json['imageUrl'],
      serviceTypes: List<String>.from(json['serviceTypes'] ?? []),
      serviceTimes: json['serviceTimes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'description': description,
      'imageUrl': imageUrl,
      'serviceTypes': serviceTypes,
      'serviceTimes': serviceTimes,
    };
  }
}

class FirestoreChurchService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get church by ID
  Future<Church?> getChurch(String churchId) async {
    try {
      final doc = await _firestore.collection('churches').doc(churchId).get();
      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return Church.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error fetching church: $e');
      return null;
    }
  }

  // Get all churches
  Future<List<Church>> getAllChurches() async {
    try {
      final querySnapshot = await _firestore.collection('churches').get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return Church.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching churches: $e');
      return [];
    }
  }

  // Search churches by name
  Future<List<Church>> searchChurches(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection('churches')
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return Church.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error searching churches: $e');
      return [];
    }
  }

  // Get churches near a location
  Future<List<Church>> getChurchesNearLocation({
    required double latitude,
    required double longitude,
    double radiusInKm = 10.0,
  }) async {
    try {
      // Note: For production, you might want to use GeoFlutterFire for more efficient geo queries
      // For now, we'll get all churches and filter client-side
      final allChurches = await getAllChurches();

      return allChurches.where((church) {
        final distance = _calculateDistance(
          latitude,
          longitude,
          church.latitude,
          church.longitude,
        );
        return distance <= radiusInKm;
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching nearby churches: $e');
      return [];
    }
  }

  // Create a new church (admin only)
  Future<String?> createChurch({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? description,
    String? imageUrl,
    List<String> serviceTypes = const ['Sunday Service', 'Midweek Service'],
    Map<String, dynamic>? serviceTimes,
  }) async {
    try {
      final churchData = {
        'name': name,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'description': description,
        'imageUrl': imageUrl,
        'serviceTypes': serviceTypes,
        'serviceTimes':
            serviceTimes ??
            {'Sunday Service': '09:00', 'Midweek Service': '18:00'},
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final docRef = await _firestore.collection('churches').add(churchData);
      debugPrint('✅ Church created successfully: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('❌ Error creating church: $e');
      return null;
    }
  }

  // Update church information
  Future<bool> updateChurch(
    String churchId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['updatedAt'] = FieldValue.serverTimestamp();
      await _firestore.collection('churches').doc(churchId).update(updates);
      debugPrint('✅ Church updated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating church: $e');
      return false;
    }
  }

  // Join a church (update user's churchId)
  Future<bool> joinChurch(String userId, String churchId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'churchId': churchId,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      debugPrint('✅ Successfully joined church');
      return true;
    } catch (e) {
      debugPrint('❌ Error joining church: $e');
      return false;
    }
  }

  // Leave a church
  Future<bool> leaveChurch(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'churchId': null,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      debugPrint('✅ Successfully left church');
      return true;
    } catch (e) {
      debugPrint('❌ Error leaving church: $e');
      return false;
    }
  }

  // Get church members
  Future<List<Map<String, dynamic>>> getChurchMembers(String churchId) async {
    try {
      final querySnapshot = await _firestore
          .collection('users')
          .where('churchId', isEqualTo: churchId)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching church members: $e');
      return [];
    }
  }

  // Calculate distance between two points (Haversine formula)
  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a =
        (dLat / 2).sin() * (dLat / 2).sin() +
        lat1.cos() * lat2.cos() * (dLon / 2).sin() * (dLon / 2).sin();

    final double c = 2 * a.sqrt().asin();

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}
