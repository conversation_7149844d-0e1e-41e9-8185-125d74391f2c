import 'dart:async';

import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/services/attendance_service.dart';
import 'package:flockin_mobile/services/church_service.dart';
import 'package:flockin_mobile/services/distance_tracker_service.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';
import 'package:flockin_mobile/widgets/common/gradient_container.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Unified attendance card that intelligently combines service info with attendance status
/// This creates a cohesive narrative: Service → Status → Context → Action
class UnifiedAttendanceCard extends StatefulWidget {
  final AutomaticAttendanceService attendanceService;
  final Church? church;
  final VoidCallback? onTap;

  const UnifiedAttendanceCard({
    super.key,
    required this.attendanceService,
    this.church,
    this.onTap,
  });

  @override
  State<UnifiedAttendanceCard> createState() => _UnifiedAttendanceCardState();
}

class _UnifiedAttendanceCardState extends State<UnifiedAttendanceCard>
    with TickerProviderStateMixin {
  AttendanceStatus _currentStatus = AttendanceStatus.absent();
  bool _isLoading = true;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // Distance tracking
  DistanceTrackerService? _distanceTracker;
  StreamSubscription<DistanceUpdate>? _distanceSubscription;
  DistanceUpdate? _currentDistance;
  bool _isWithinGeofence = false;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _initializeStatus();
    _listenToStatusUpdates();
    _initializeDistanceTracking();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _distanceSubscription?.cancel();
    _distanceTracker?.dispose();
    super.dispose();
  }

  Future<void> _initializeStatus() async {
    try {
      final status = await widget.attendanceService.getCurrentStatus();
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _listenToStatusUpdates() {
    widget.attendanceService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _isLoading = false;
        });
      }
    });
  }

  void _initializeDistanceTracking() {
    if (widget.church != null) {
      _distanceTracker = DistanceTrackerService();

      // Initialize with church data to avoid API calls
      _distanceTracker!.initializeWithChurch(widget.church!);

      // Start tracking
      _distanceTracker!
          .startTracking(church: widget.church!)
          .then((_) {
            // Listen to distance updates
            _distanceSubscription = _distanceTracker!.distanceStream.listen((
              distanceUpdate,
            ) {
              if (mounted) {
                setState(() {
                  _currentDistance = distanceUpdate;
                  _isWithinGeofence = distanceUpdate.isWithinGeofence;
                });
              }
            });
          })
          .catchError((error) {
            if (kDebugMode) {
              print('Error starting distance tracking: $error');
            }
          });
    }
  }

  @override
  void didUpdateWidget(UnifiedAttendanceCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If church data changed, refresh distance tracking
    if (oldWidget.church != widget.church && widget.church != null) {
      _refreshDistanceTracking();
    }
  }

  void _refreshDistanceTracking() {
    // Cancel existing subscription
    _distanceSubscription?.cancel();
    _distanceTracker?.dispose();

    // Reinitialize with new church data
    _initializeDistanceTracking();

    if (kDebugMode) {
      print('🔄 Distance tracking refreshed with new church data');
    }
  }

  void _manualCheckIn() async {
    // Double-check service time before proceeding
    if (!_isCurrentlyServiceTime()) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Cannot check in - no active service'),
            backgroundColor: Color(0xFFEF4444),
          ),
        );
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Create a direct attendance service instance for manual check-in
        final attendanceService = AttendanceService();
        final geolocationService = GeolocationService();

        // Get current location
        final location = await geolocationService.getCurrentLocation();
        if (location == null) {
          throw Exception('Unable to get current location');
        }

        // Attempt manual check-in
        final result = await attendanceService.checkIn();

        if (result != null) {
          // Success - update status in the automatic attendance service
          final status = AttendanceStatus.present(
            checkInTime: DateTime.now(),
            location: location,
          );

          await widget.attendanceService.updateAttendanceStatus(status);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('✅ Manual check-in successful!'),
                backgroundColor: Color(0xFF10B981),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          final errorMessage = e.toString();

          // Check if it's a geofence error (expected when user is far from church)
          if (errorMessage.contains('Location is outside church geofence') ||
              errorMessage.contains('Distance:')) {
            // Show a more user-friendly message for geofence errors
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  '📍 You need to be closer to church to check in',
                ),
                backgroundColor: const Color(0xFF6B7280), // Gray instead of red
                duration: const Duration(seconds: 2),
              ),
            );
          } else {
            // Show actual errors in red
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('❌ Check-in failed: $errorMessage'),
                backgroundColor: const Color(0xFFEF4444),
              ),
            );
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          // Refresh status after check-in attempt
          final status = await widget.attendanceService.getCurrentStatus();
          setState(() {
            _currentStatus = status;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2D3A),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            _buildServiceHeader(),
            _buildAttendanceStatus(),
            _buildLocationContext(),
            _buildDebugSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceHeader() {
    final serviceInfo = _getServiceInfo();
    final dayFormat = DateFormat('EEEE, MMMM d, y');

    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.calendar_today,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  serviceInfo.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  serviceInfo.description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        dayFormat.format(serviceInfo.date),
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: serviceInfo.isToday
                            ? const Color(0xFF3B82F6)
                            : const Color(0xFF10B981),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            serviceInfo.isToday
                                ? Icons.location_on
                                : Icons.schedule,
                            color: Colors.white,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            serviceInfo.serviceName == "No Service Scheduled" ||
                                    serviceInfo.serviceName ==
                                        "No Upcoming Service"
                                ? "No Service"
                                : serviceInfo.serviceName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceStatus() {
    return AnimatedGradientContainer(
      gradient: _getStatusGradient(),
      borderRadius: BorderRadius.circular(16),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          _buildStatusIcon(),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusTitle(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStatusSubtitle(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationContext() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(_getLocationIcon(), color: _getLocationIconColor(), size: 16),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getLocationMessage(),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (!_isLoading && !_currentStatus.isPresent)
            Icon(
              Icons.refresh,
              color: Colors.white.withValues(alpha: 0.6),
              size: 16,
            ),
        ],
      ),
    );
  }

  Widget _buildDebugSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937).withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.bug_report, color: Color(0xFF10B981), size: 16),
              const SizedBox(width: 8),
              const Text(
                'Debug Info',
                style: TextStyle(
                  color: Color(0xFF10B981),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: _isCurrentlyServiceTime() ? _manualCheckIn : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isCurrentlyServiceTime()
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF6B7280),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  minimumSize: Size.zero,
                ),
                child: Text(
                  _isCurrentlyServiceTime() ? 'Manual Check-In' : 'No Service',
                  style: TextStyle(
                    color: _isCurrentlyServiceTime()
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Current time: ${DateTime.now().toString().substring(11, 19)}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Status: ${_currentStatus.isPresent ? "Present" : "Absent"}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  LinearGradient _getStatusGradient() {
    if (_isLoading) {
      return const LinearGradient(
        colors: [Color(0xFF6B7280), Color(0xFF4B5563)],
      );
    }

    // Show green if either officially checked in OR within geofence
    if (_currentStatus.isPresent || _isWithinGeofence) {
      return const LinearGradient(
        colors: [Color(0xFF10B981), Color(0xFF059669)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    } else {
      return const LinearGradient(
        colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
  }

  Widget _buildStatusIcon() {
    if (_isLoading) {
      return AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          _pulseController.repeat(reverse: true);
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: const Icon(Icons.sync, color: Colors.white, size: 28),
          );
        },
      );
    }

    return Icon(
      (_currentStatus.isPresent || _isWithinGeofence)
          ? Icons.check_circle
          : Icons.location_off,
      color: Colors.white,
      size: 28,
    );
  }

  String _getStatusTitle() {
    if (_isLoading) return 'Checking Status...';

    // Check if there's an active service
    final isServiceActive = _isCurrentlyServiceTime();

    // If no service is active and user is at church, show different message
    if (!isServiceActive && (_currentStatus.isPresent || _isWithinGeofence)) {
      return 'At Church';
    }

    // Show "Present" if either officially checked in OR within geofence (during service time)
    if (_currentStatus.isPresent || _isWithinGeofence) {
      return 'Present';
    }

    return 'Absent';
  }

  String _getStatusSubtitle() {
    if (_isLoading) return 'Detecting your location...';

    // Check if there's an active service
    final isServiceActive = _isCurrentlyServiceTime();

    // If officially checked in with time
    if (_currentStatus.isPresent && _currentStatus.checkInTime != null) {
      final timeFormat = DateFormat('hh:mm a');
      return 'Checked in at ${timeFormat.format(_currentStatus.checkInTime!)}';
    }

    // If within geofence but no active service
    if (!isServiceActive && _isWithinGeofence) {
      if (_currentDistance != null) {
        return 'No service in session (${_currentDistance!.distanceMeters.round()}m away)';
      }
      return 'No service in session - enjoy your visit!';
    }

    // If within geofence during service time
    if (_isWithinGeofence) {
      if (_currentDistance != null) {
        return 'At church (${_currentDistance!.distanceMeters.round()}m away)';
      }
      return 'At Church Premises';
    }

    // If officially present but no geofence data
    if (_currentStatus.isPresent) {
      return 'At Church Premises';
    }

    // Default absent message
    return 'Not at church';
  }

  IconData _getLocationIcon() {
    if (_isLoading) return Icons.location_searching;
    if (_currentStatus.isPresent) return Icons.location_on;
    return Icons.info_outline;
  }

  Color _getLocationIconColor() {
    if (_isLoading) return Colors.orange;
    if (_currentStatus.isPresent) return Colors.green;
    return Colors.blue;
  }

  String _getLocationMessage() {
    if (_isLoading) {
      return 'Locating...';
    }

    // Check if there's an active service
    final isServiceActive = _isCurrentlyServiceTime();

    if (_currentStatus.isPresent) {
      return 'At Church Premises';
    } else {
      // Show different message based on service status
      if (isServiceActive) {
        return 'You will be automatically checked in when you arrive at church';
      } else {
        return 'Attendance tracking is paused - no active service';
      }
    }
  }

  /// Check if current time falls within any configured service time
  bool _isCurrentlyServiceTime() {
    if (widget.church?.serviceTimes == null ||
        widget.church!.serviceTimes.isEmpty) {
      // If no service times are configured, don't allow attendance tracking
      return false;
    }

    final now = DateTime.now();
    final currentDay = _getDayOfWeek(now.weekday);
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    // Check if current time falls within any service time
    for (final serviceTime in widget.church!.serviceTimes) {
      // Check if it's the right day
      if (serviceTime.day.toLowerCase() == currentDay.toLowerCase()) {
        // Check if current time is within service time range
        if (_isTimeInRange(
          currentTime,
          serviceTime.startTime,
          serviceTime.endTime,
        )) {
          return true;
        }
      }
    }

    return false;
  }

  /// Get today's service or next upcoming service
  ServiceInfo _getServiceInfo() {
    if (widget.church?.serviceTimes == null ||
        widget.church!.serviceTimes.isEmpty) {
      return ServiceInfo(
        title: "Today's Service",
        serviceName: "No Service Scheduled",
        date: DateTime.now(),
        isToday: true,
      );
    }

    final now = DateTime.now();
    final currentDay = _getDayOfWeek(now.weekday);
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    if (kDebugMode) {
      print('📅 Service detection: $currentDay $currentTime');
      print(
        '📅 Available services: ${widget.church!.serviceTimes.map((s) => '${s.day} ${s.startTime}-${s.endTime} ${s.name}').join(', ')}',
      );
    }

    // First, check if there's a service today
    for (final serviceTime in widget.church!.serviceTimes) {
      if (serviceTime.day.toLowerCase() == currentDay.toLowerCase()) {
        // Check if service is still ongoing or upcoming today
        if (_isTimeInRange(
              currentTime,
              serviceTime.startTime,
              serviceTime.endTime,
            ) ||
            _parseTime(currentTime) < _parseTime(serviceTime.startTime)) {
          return ServiceInfo(
            title: "Today's Service",
            serviceName: serviceTime.name,
            date: now,
            isToday: true,
            startTime: serviceTime.startTime,
            endTime: serviceTime.endTime,
          );
        }
      }
    }

    // No service today or today's service is over, find next service
    final nextService = _findNextService();
    if (nextService != null) {
      return ServiceInfo(
        title: "Next Service",
        serviceName: nextService.name,
        date: nextService.date,
        isToday: false,
        startTime: nextService.startTime,
        endTime: nextService.endTime,
      );
    }

    // No upcoming services found
    return ServiceInfo(
      title: "Next Service",
      serviceName: "No Upcoming Service",
      date: now,
      isToday: false,
    );
  }

  /// Find the next upcoming service
  ServiceTimeWithDate? _findNextService() {
    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    // Look for services in the next 7 days
    for (int dayOffset = 0; dayOffset < 7; dayOffset++) {
      final checkDate = now.add(Duration(days: dayOffset));
      final checkDay = _getDayOfWeek(checkDate.weekday);

      for (final serviceTime in widget.church!.serviceTimes) {
        if (serviceTime.day.toLowerCase() == checkDay.toLowerCase()) {
          // If it's today, only consider future services
          if (dayOffset == 0) {
            if (_parseTime(currentTime) < _parseTime(serviceTime.startTime)) {
              return ServiceTimeWithDate(
                name: serviceTime.name,
                startTime: serviceTime.startTime,
                endTime: serviceTime.endTime,
                date: checkDate,
              );
            }
          } else {
            // Future days, any service is valid
            return ServiceTimeWithDate(
              name: serviceTime.name,
              startTime: serviceTime.startTime,
              endTime: serviceTime.endTime,
              date: checkDate,
            );
          }
        }
      }
    }

    return null;
  }

  /// Convert weekday number to day name
  String _getDayOfWeek(int weekday) {
    switch (weekday) {
      case 1:
        return 'monday';
      case 2:
        return 'tuesday';
      case 3:
        return 'wednesday';
      case 4:
        return 'thursday';
      case 5:
        return 'friday';
      case 6:
        return 'saturday';
      case 7:
        return 'sunday';
      default:
        return 'unknown';
    }
  }

  /// Check if a time is within a given range
  bool _isTimeInRange(String currentTime, String startTime, String endTime) {
    try {
      final current = _parseTime(currentTime);
      final start = _parseTime(startTime);
      final end = _parseTime(endTime);
      return current >= start && current <= end;
    } catch (e) {
      return false;
    }
  }

  /// Parse time string (HH:MM) to minutes since midnight
  int _parseTime(String timeString) {
    final parts = timeString.split(':');
    if (parts.length != 2) return 0;
    final hours = int.tryParse(parts[0]) ?? 0;
    final minutes = int.tryParse(parts[1]) ?? 0;
    return hours * 60 + minutes;
  }
}

/// Service information data class
class ServiceInfo {
  final String title;
  final String serviceName;
  final DateTime date;
  final bool isToday;
  final String? startTime;
  final String? endTime;

  ServiceInfo({
    required this.title,
    required this.serviceName,
    required this.date,
    required this.isToday,
    this.startTime,
    this.endTime,
  });

  /// Get a user-friendly description
  String get description {
    if (serviceName == "No Service Scheduled" ||
        serviceName == "No Upcoming Service") {
      return serviceName;
    }

    if (isToday) {
      if (startTime != null && endTime != null) {
        return '$serviceName ($startTime - $endTime)';
      }
      return serviceName;
    } else {
      // For next service, show day and time
      final dayName = DateFormat('EEEE').format(date);
      if (startTime != null) {
        return '$serviceName on $dayName at $startTime';
      }
      return '$serviceName on $dayName';
    }
  }
}

/// Service time with date
class ServiceTimeWithDate {
  final String name;
  final String startTime;
  final String endTime;
  final DateTime date;

  ServiceTimeWithDate({
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.date,
  });
}
