import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';

/// Service responsible for initializing the app and setting up automatic attendance
class AppInitializationService {
  final AutomaticAttendanceService _automaticAttendanceService;
  final GeolocationService _geolocationService;

  AppInitializationService({
    required AutomaticAttendanceService automaticAttendanceService,
    required GeolocationService geolocationService,
  }) : _automaticAttendanceService = automaticAttendanceService,
       _geolocationService = geolocationService;

  /// Initialize the app with automatic attendance features
  Future<AppInitializationResult> initializeApp() async {
    try {
      if (kDebugMode) {
        print('Starting app initialization...');
      }

      // Step 1: Check and request location permissions
      final permissionResult = await _handleLocationPermissions();
      if (!permissionResult.isGranted) {
        return AppInitializationResult.permissionDenied(
          'Location permission is required for automatic attendance',
          permissionResult,
        );
      }

      // Step 2: Initialize automatic attendance service
      await _automaticAttendanceService.initialize();

      // Step 3: Enable automatic attendance if permissions are granted
      if (permissionResult.isGranted) {
        await _automaticAttendanceService.enableService();
      }

      if (kDebugMode) {
        print('App initialization completed successfully');
      }

      return AppInitializationResult.success(
        'Automatic attendance is now active',
        permissionResult,
      );
    } catch (e) {
      if (kDebugMode) {
        print('App initialization failed: $e');
      }

      return AppInitializationResult.failed(
        'Failed to initialize automatic attendance: ${e.toString()}',
      );
    }
  }

  /// Handle location permissions with user-friendly flow
  Future<LocationPermissionResult> _handleLocationPermissions() async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await _geolocationService
          .isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionResult.serviceDisabled(
          'Location services are disabled. Please enable them in device settings.',
        );
      }

      // Check current permission status
      final currentPermission = await _geolocationService.getPermissionStatus();

      if (currentPermission == LocationPermission.whileInUse ||
          currentPermission == LocationPermission.always) {
        return LocationPermissionResult.granted(
          'Location permission already granted',
        );
      }

      // Request permissions
      final hasPermissions = await _geolocationService
          .checkAndRequestPermissions();

      if (hasPermissions) {
        return LocationPermissionResult.granted(
          'Location permission granted successfully',
        );
      } else {
        return LocationPermissionResult.denied(
          'Location permission is required for automatic attendance tracking',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling location permissions: $e');
      }

      return LocationPermissionResult.error(
        'Error requesting location permissions: ${e.toString()}',
      );
    }
  }

  /// Show permission request dialog to user
  Future<bool> showPermissionRequestDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Location Permission Required'),
              content: const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Flockin needs location access to automatically track your attendance when you arrive at church.',
                    style: TextStyle(fontSize: 16),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Features enabled with location access:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Automatic check-in when you arrive'),
                  Text('• No need to manually mark attendance'),
                  Text('• Accurate attendance tracking'),
                  Text('• Battery-optimized location monitoring'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Not Now'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Grant Permission'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Show settings dialog when permission is permanently denied
  Future<bool> showSettingsDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Permission Required'),
              content: const Text(
                'Location permission has been permanently denied. '
                'Please enable it in app settings to use automatic attendance.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.of(context).pop(true);
                    await openAppSettings();
                  },
                  child: const Text('Open Settings'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Check if automatic attendance is properly set up
  Future<bool> isAutomaticAttendanceSetup() async {
    try {
      // Check if service is enabled
      final isServiceEnabled = await _automaticAttendanceService
          .isServiceEnabled();

      // Check if permissions are granted
      final hasPermissions = await _geolocationService
          .checkAndRequestPermissions();

      return isServiceEnabled && hasPermissions;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking automatic attendance setup: $e');
      }
      return false;
    }
  }

  /// Disable automatic attendance (for user preference)
  Future<void> disableAutomaticAttendance() async {
    await _automaticAttendanceService.disableService();

    if (kDebugMode) {
      print('Automatic attendance disabled by user');
    }
  }

  /// Re-enable automatic attendance
  Future<AppInitializationResult> enableAutomaticAttendance() async {
    return await initializeApp();
  }
}

/// Result of app initialization
class AppInitializationResult {
  final AppInitializationStatus status;
  final String message;
  final LocationPermissionResult? permissionResult;

  AppInitializationResult._({
    required this.status,
    required this.message,
    this.permissionResult,
  });

  factory AppInitializationResult.success(
    String message,
    LocationPermissionResult permissionResult,
  ) {
    return AppInitializationResult._(
      status: AppInitializationStatus.success,
      message: message,
      permissionResult: permissionResult,
    );
  }

  factory AppInitializationResult.permissionDenied(
    String message,
    LocationPermissionResult permissionResult,
  ) {
    return AppInitializationResult._(
      status: AppInitializationStatus.permissionDenied,
      message: message,
      permissionResult: permissionResult,
    );
  }

  factory AppInitializationResult.failed(String message) {
    return AppInitializationResult._(
      status: AppInitializationStatus.failed,
      message: message,
    );
  }

  bool get isSuccess => status == AppInitializationStatus.success;
  bool get isPermissionDenied =>
      status == AppInitializationStatus.permissionDenied;
  bool get isFailed => status == AppInitializationStatus.failed;
}

enum AppInitializationStatus { success, permissionDenied, failed }

/// Result of location permission handling
class LocationPermissionResult {
  final LocationPermissionStatus status;
  final String message;

  LocationPermissionResult._({required this.status, required this.message});

  factory LocationPermissionResult.granted(String message) {
    return LocationPermissionResult._(
      status: LocationPermissionStatus.granted,
      message: message,
    );
  }

  factory LocationPermissionResult.denied(String message) {
    return LocationPermissionResult._(
      status: LocationPermissionStatus.denied,
      message: message,
    );
  }

  factory LocationPermissionResult.serviceDisabled(String message) {
    return LocationPermissionResult._(
      status: LocationPermissionStatus.serviceDisabled,
      message: message,
    );
  }

  factory LocationPermissionResult.error(String message) {
    return LocationPermissionResult._(
      status: LocationPermissionStatus.error,
      message: message,
    );
  }

  bool get isGranted => status == LocationPermissionStatus.granted;
  bool get isDenied => status == LocationPermissionStatus.denied;
  bool get isServiceDisabled =>
      status == LocationPermissionStatus.serviceDisabled;
  bool get isError => status == LocationPermissionStatus.error;
}

enum LocationPermissionStatus { granted, denied, serviceDisabled, error }
