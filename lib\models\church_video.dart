class ChurchVideo {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String duration;
  final DateTime uploadDate;
  final String category;
  final int views;
  final String? channelName;
  final String? channelId;

  ChurchVideo({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.duration,
    required this.uploadDate,
    required this.category,
    required this.views,
    this.channelName,
    this.channelId,
  });

  factory ChurchVideo.fromJson(Map<String, dynamic> json) {
    return ChurchVideo(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? json['thumbnail_url'] ?? '',
      duration: json['duration'] ?? '0:00',
      uploadDate: json['uploadDate'] != null 
          ? DateTime.parse(json['uploadDate']) 
          : DateTime.now(),
      category: json['category'] ?? 'General',
      views: json['views'] ?? 0,
      channelName: json['channelName'] ?? json['channel_name'],
      channelId: json['channelId'] ?? json['channel_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'uploadDate': uploadDate.toIso8601String(),
      'category': category,
      'views': views,
      'channelName': channelName,
      'channelId': channelId,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChurchVideo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChurchVideo(id: $id, title: $title, category: $category)';
  }
}
