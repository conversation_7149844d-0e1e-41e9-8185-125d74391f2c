rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to get user data
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    // Helper function to check if user is admin or pastor
    function isAdmin() {
      return isAuthenticated() && getUserData().role in ['admin', 'pastor'];
    }

    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      allow read: if isAdmin(); // <PERSON><PERSON> can read all user profiles
    }

    // Churches can be read by anyone authenticated, but only admins can write
    match /churches/{churchId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Attendance records
    match /attendance/{attendanceId} {
      allow read: if isAuthenticated() && (
        // User can read their own attendance
        resource.data.memberId == request.auth.uid ||
        // Church admins can read all attendance for their church
        (isAdmin() && getUserData().churchId == resource.data.churchId)
      );
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.memberId;
      allow update: if isAuthenticated() && (
        request.auth.uid == resource.data.memberId ||
        (isAdmin() && getUserData().churchId == resource.data.churchId)
      );
    }

    // Church videos - read by authenticated users, write by admins
    match /church_videos/{videoId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Test collection for connection testing
    match /test/{testId} {
      allow read, write: if isAuthenticated();
    }
  }
}
