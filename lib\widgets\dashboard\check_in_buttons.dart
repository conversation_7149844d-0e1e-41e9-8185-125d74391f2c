import 'package:flutter/material.dart';

import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/widgets/common/gradient_container.dart';

class CheckInButtons extends StatelessWidget {
  final bool isCheckedIn;
  final VoidCallback? onCheckIn;
  final VoidCallback? onCheckOut;

  const CheckInButtons({
    super.key,
    required this.isCheckedIn,
    this.onCheckIn,
    this.onCheckOut,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: AnimatedGradientContainer(
            gradient: isCheckedIn
                ? LinearGradient(
                    colors: [Colors.grey.shade600, Colors.grey.shade700],
                  )
                : AppColors.checkInGradient,
            borderRadius: BorderRadius.circular(20),
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
            onTap: isCheckedIn ? null : onCheckIn,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isCheckedIn ? Icons.check : Icons.login,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isCheckedIn ? 'Checked In' : 'Check In',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AnimatedGradientContainer(
            gradient: !isCheckedIn
                ? LinearGradient(
                    colors: [Colors.grey.shade600, Colors.grey.shade700],
                  )
                : AppColors.checkOutGradient,
            borderRadius: BorderRadius.circular(20),
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
            onTap: !isCheckedIn ? null : onCheckOut,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  !isCheckedIn ? Icons.block : Icons.logout,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  !isCheckedIn ? 'Not Available' : 'Check Out',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
