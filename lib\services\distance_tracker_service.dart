import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

import 'package:flockin_mobile/models/user_model.dart';
import 'package:flockin_mobile/services/attendance_service.dart';
import 'package:flockin_mobile/services/automatic_attendance_logic.dart';
import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/services/church_service.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';

/// Service for real-time distance tracking to church location
class DistanceTrackerService {
  static const Duration _updateInterval = Duration(seconds: 2);
  static const double _earthRadiusKm = 6371.0;
  static const double _significantDistanceChangeMeters =
      5.0; // Only update UI if distance changes by 5+ meters

  final GeolocationService _geolocationService = GeolocationService();
  final ChurchService _churchService = ChurchService();
  final AttendanceService _attendanceService = AttendanceService();

  AutomaticAttendanceService? _automaticAttendanceService;
  AutomaticAttendanceLogic? _attendanceLogic;

  // Stream controllers for real-time updates
  final StreamController<DistanceUpdate> _distanceController =
      StreamController<DistanceUpdate>.broadcast();
  final StreamController<LocationData> _locationController =
      StreamController<LocationData>.broadcast();
  final StreamController<AttendanceNotification> _notificationController =
      StreamController<AttendanceNotification>.broadcast();

  // Private state
  Timer? _updateTimer;
  StreamSubscription<Position>? _locationSubscription;
  Church? _currentChurch;
  DistanceUpdate? _lastDistanceUpdate;
  bool _isTracking = false;
  bool _isInitialized = false;
  bool _wasWithinGeofence = false; // Track previous geofence state

  // Public streams
  Stream<DistanceUpdate> get distanceStream => _distanceController.stream;
  Stream<LocationData> get locationStream => _locationController.stream;
  Stream<AttendanceNotification> get notificationStream =>
      _notificationController.stream;

  // Getters
  bool get isTracking => _isTracking;
  DistanceUpdate? get lastDistanceUpdate => _lastDistanceUpdate;

  /// Initialize the distance tracker with user's church
  Future<void> initialize({
    User? user,
    AutomaticAttendanceService? attendanceService,
  }) async {
    if (kDebugMode) {
      print('Initializing DistanceTrackerService');
    }

    try {
      if (user?.churchId == null || user!.churchId!.isEmpty) {
        throw Exception('User has no church assigned');
      }

      if (kDebugMode) {
        print('Fetching church data for churchId: ${user.churchId}');
      }

      // Get church information by ID
      _currentChurch = await _churchService.getChurchById(user.churchId!);

      if (_currentChurch == null) {
        throw Exception('Church not found in database');
      }

      if (_currentChurch!.latitude == null ||
          _currentChurch!.longitude == null) {
        throw Exception(
          'Church location coordinates not configured. Please set church location in the admin dashboard.',
        );
      }

      // Set the attendance service (use provided one or create new)
      _automaticAttendanceService =
          attendanceService ?? AutomaticAttendanceService();

      if (kDebugMode) {
        print(
          '🔧 Using ${attendanceService != null ? 'shared' : 'new'} AutomaticAttendanceService instance',
        );
      }

      // Initialize attendance logic if not already initialized
      _attendanceLogic ??= AutomaticAttendanceLogic(
        attendanceService: _attendanceService,
        geolocationService: _geolocationService,
        automaticAttendanceService: _automaticAttendanceService!,
      );
      _isInitialized = true;

      if (kDebugMode) {
        print('Successfully fetched church: ${_currentChurch!.name}');
        print(
          'Church coordinates: ${_currentChurch!.latitude}, ${_currentChurch!.longitude}',
        );
        print('Geofence radius: ${_currentChurch!.geofenceRadius}m');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing distance tracker: $e');
      }
      rethrow;
    }
  }

  /// Initialize with an existing church object (to avoid duplicate API calls)
  void initializeWithChurch(Church church) {
    _currentChurch = church;
    if (kDebugMode) {
      print(
        'Initialized distance tracker with existing church: ${church.name}',
      );
      print('Church coordinates: ${church.latitude}, ${church.longitude}');
    }
  }

  /// Start real-time distance tracking
  Future<void> startTracking({
    User? user,
    Church? church,
    AutomaticAttendanceService? attendanceService,
  }) async {
    if (_isTracking) {
      if (kDebugMode) {
        print('Distance tracking already active');
      }
      return;
    }

    // If church object is provided, use it directly
    if (church != null) {
      initializeWithChurch(church);
      // Still need to set up attendance service
      _automaticAttendanceService =
          attendanceService ?? AutomaticAttendanceService();
      _attendanceLogic ??= AutomaticAttendanceLogic(
        attendanceService: _attendanceService,
        geolocationService: _geolocationService,
        automaticAttendanceService: _automaticAttendanceService!,
      );
      _isInitialized = true;
    } else if (_currentChurch == null) {
      await initialize(user: user, attendanceService: attendanceService);
    }

    // Check permissions
    final hasPermissions = await _geolocationService
        .checkAndRequestPermissions();
    if (!hasPermissions) {
      throw Exception('Location permissions required for distance tracking');
    }

    _isTracking = true;

    // Start location updates with high frequency for smooth distance updates
    _locationSubscription = _geolocationService.startLocationUpdates(
      onLocationUpdate: _handleLocationUpdate,
      onError: (error) {
        if (kDebugMode) {
          print('Location update error: $error');
        }
      },
      intervalMs: 1000, // Update every second for smooth tracking
    );

    // Start periodic distance calculations
    _updateTimer = Timer.periodic(
      _updateInterval,
      (_) => _calculateAndUpdateDistance(),
    );

    if (kDebugMode) {
      print('Distance tracking started');
    }
  }

  /// Stop distance tracking
  void stopTracking() {
    if (!_isTracking) return;

    _isTracking = false;
    _locationSubscription?.cancel();
    _locationSubscription = null;
    _updateTimer?.cancel();
    _updateTimer = null;

    if (kDebugMode) {
      print('Distance tracking stopped');
    }
  }

  /// Handle location updates from geolocation service
  void _handleLocationUpdate(LocationData location) {
    _locationController.add(location);
    _calculateDistanceToChurch(location);
  }

  /// Calculate distance and update if significant change
  Future<void> _calculateAndUpdateDistance() async {
    try {
      final currentLocation = await _geolocationService.getCurrentLocation();
      if (currentLocation != null) {
        _calculateDistanceToChurch(currentLocation);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating distance: $e');
      }
    }
  }

  /// Calculate distance to church and emit update if significant
  void _calculateDistanceToChurch(LocationData userLocation) {
    if (_currentChurch?.latitude == null || _currentChurch?.longitude == null) {
      return;
    }

    if (kDebugMode) {
      print('=== DISTANCE CALCULATION DEBUG ===');
      print(
        'User location: ${userLocation.latitude}, ${userLocation.longitude}',
      );
      print(
        'Church location: ${_currentChurch!.latitude}, ${_currentChurch!.longitude}',
      );
      print('User location accuracy: ${userLocation.accuracy}m');
    }

    final distanceMeters = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      _currentChurch!.latitude!,
      _currentChurch!.longitude!,
    );

    if (kDebugMode) {
      print(
        'Calculated distance: ${distanceMeters}m (${(distanceMeters / 1000).toStringAsFixed(2)}km)',
      );
      print('=== END DEBUG ===');
    }

    final distanceUpdate = DistanceUpdate(
      distanceMeters: distanceMeters,
      userLocation: userLocation,
      churchLocation: ChurchLocation(
        latitude: _currentChurch!.latitude!,
        longitude: _currentChurch!.longitude!,
        name: _currentChurch!.name,
        address: _currentChurch!.address,
      ),
      timestamp: DateTime.now(),
      isWithinGeofence: distanceMeters <= _currentChurch!.geofenceRadius,
      geofenceRadius: _currentChurch!.geofenceRadius.toDouble(),
    );

    // Only emit update if distance changed significantly or first update
    if (_lastDistanceUpdate == null ||
        (distanceUpdate.distanceMeters - _lastDistanceUpdate!.distanceMeters)
                .abs() >=
            _significantDistanceChangeMeters) {
      _lastDistanceUpdate = distanceUpdate;
      _distanceController.add(distanceUpdate);

      if (kDebugMode) {
        print(
          '📍 Distance: ${distanceUpdate.formattedDistance} to ${_currentChurch!.name}',
        );
        print(
          '🎯 Geofence: ${distanceUpdate.distanceMeters.round()}m <= ${_currentChurch!.geofenceRadius}m = ${distanceUpdate.isWithinGeofence}',
        );
      }

      // Check for geofence entry/exit and trigger attendance logic
      _handleGeofenceChange(distanceUpdate);
    }
  }

  /// Handle geofence entry/exit and trigger automatic attendance
  void _handleGeofenceChange(DistanceUpdate distanceUpdate) {
    if (!_isInitialized) return;

    final isCurrentlyWithinGeofence = distanceUpdate.isWithinGeofence;

    if (kDebugMode) {
      print(
        '🔄 Geofence state: was=$_wasWithinGeofence, now=$isCurrentlyWithinGeofence',
      );
    }

    // Check if user just entered the geofence
    if (isCurrentlyWithinGeofence && !_wasWithinGeofence) {
      if (kDebugMode) {
        print(
          '🎯 User entered geofence! Triggering automatic attendance check...',
        );
      }
      _triggerAutomaticAttendance();
    }
    // Check if user just left the geofence
    else if (!isCurrentlyWithinGeofence && _wasWithinGeofence) {
      if (kDebugMode) {
        print('🚪 User left geofence area');
      }
      // Could trigger check-out logic here if needed
    }
    // User is within geofence but was already within
    else if (isCurrentlyWithinGeofence && _wasWithinGeofence) {
      if (kDebugMode) {
        print('✅ User remains within geofence');
      }
    }
    // User is outside geofence and was already outside
    else {
      if (kDebugMode) {
        print('❌ User remains outside geofence');
      }
    }

    // Update the previous state
    _wasWithinGeofence = isCurrentlyWithinGeofence;
  }

  /// Trigger automatic attendance check
  void _triggerAutomaticAttendance() async {
    try {
      if (kDebugMode) {
        print('Performing automatic attendance check...');
      }

      // Check if it's currently service time
      if (!_isCurrentlyServiceTime()) {
        if (kDebugMode) {
          print('⏰ Not currently service time - skipping automatic attendance');
        }
        _notificationController.add(
          AttendanceNotification.info(
            'Not currently service time - automatic attendance disabled',
          ),
        );
        return;
      }

      if (kDebugMode) {
        print('🔄 Calling attendance logic...');
      }

      if (_attendanceLogic == null) {
        if (kDebugMode) {
          print('❌ Attendance logic not initialized');
        }
        return;
      }

      final result = await _attendanceLogic!.checkAndHandleAttendance();

      if (kDebugMode) {
        print('✅ Attendance check result: ${result.message}');
        print('📊 Result type: ${result.runtimeType}');
      }

      // Emit notification based on result
      _emitAttendanceNotification(result);
    } catch (e) {
      if (kDebugMode) {
        print('Error during automatic attendance: $e');
      }
      _notificationController.add(
        AttendanceNotification.error(
          'Failed to check attendance: ${e.toString()}',
        ),
      );
    }
  }

  /// Emit appropriate notification based on attendance result
  void _emitAttendanceNotification(dynamic result) {
    // Get current service name if available
    String? currentServiceName = _getCurrentServiceName();

    if (result.message.contains('success') ||
        result.message.contains('checked in')) {
      _notificationController.add(
        AttendanceNotification.success(
          '✅ Automatically checked in to ${_currentChurch?.name ?? 'church'}',
          serviceName: currentServiceName,
        ),
      );
    } else if (result.message.contains('failed') ||
        result.message.contains('error')) {
      _notificationController.add(
        AttendanceNotification.error('❌ ${result.message}'),
      );
    } else {
      _notificationController.add(
        AttendanceNotification.info('ℹ️ ${result.message}'),
      );
    }
  }

  /// Get the name of the current service if within service time
  String? _getCurrentServiceName() {
    if (_currentChurch?.serviceTimes == null ||
        _currentChurch!.serviceTimes.isEmpty) {
      return null;
    }

    final now = DateTime.now();
    final currentDay = _getDayOfWeek(now.weekday);
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    for (final serviceTime in _currentChurch!.serviceTimes) {
      if (serviceTime.day.toLowerCase() == currentDay.toLowerCase()) {
        if (_isTimeInRange(
          currentTime,
          serviceTime.startTime,
          serviceTime.endTime,
        )) {
          return serviceTime.name;
        }
      }
    }

    return null;
  }

  /// Check if current time falls within any configured service time
  bool _isCurrentlyServiceTime() {
    if (_currentChurch?.serviceTimes == null ||
        _currentChurch!.serviceTimes.isEmpty) {
      if (kDebugMode) {
        print('⚠️ No service times configured - allowing attendance');
      }
      return true; // Allow attendance if no service times are configured
    }

    final now = DateTime.now();
    final currentDay = _getDayOfWeek(now.weekday);
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    if (kDebugMode) {
      print('🕐 Service time check:');
      print('   Current: $currentDay $currentTime');
      print('   Available service times:');
      for (final serviceTime in _currentChurch!.serviceTimes) {
        print(
          '     ${serviceTime.day} ${serviceTime.startTime}-${serviceTime.endTime} (${serviceTime.name})',
        );
      }
    }

    for (final serviceTime in _currentChurch!.serviceTimes) {
      // Check if it's the right day
      if (serviceTime.day.toLowerCase() == currentDay.toLowerCase()) {
        // Check if current time is within service time range
        if (_isTimeInRange(
          currentTime,
          serviceTime.startTime,
          serviceTime.endTime,
        )) {
          if (kDebugMode) {
            print(
              '✅ Currently ${serviceTime.name} time (${serviceTime.startTime}-${serviceTime.endTime})',
            );
          }
          return true;
        } else {
          if (kDebugMode) {
            print(
              '   ❌ ${serviceTime.name}: $currentTime not in ${serviceTime.startTime}-${serviceTime.endTime}',
            );
          }
        }
      } else {
        if (kDebugMode) {
          print('   ❌ ${serviceTime.name}: $currentDay != ${serviceTime.day}');
        }
      }
    }

    if (kDebugMode) {
      print('❌ Not currently service time. Current: $currentDay $currentTime');
    }
    return false;
  }

  /// Convert weekday number to day name
  String _getDayOfWeek(int weekday) {
    switch (weekday) {
      case 1:
        return 'monday';
      case 2:
        return 'tuesday';
      case 3:
        return 'wednesday';
      case 4:
        return 'thursday';
      case 5:
        return 'friday';
      case 6:
        return 'saturday';
      case 7:
        return 'sunday';
      default:
        return 'unknown';
    }
  }

  /// Check if a time is within a given range
  bool _isTimeInRange(String currentTime, String startTime, String endTime) {
    try {
      final current = _parseTime(currentTime);
      final start = _parseTime(startTime);
      final end = _parseTime(endTime);

      return current >= start && current <= end;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing time: $e');
      }
      return false;
    }
  }

  /// Parse time string (HH:MM) to minutes since midnight
  int _parseTime(String timeString) {
    final parts = timeString.split(':');
    if (parts.length != 2) {
      throw FormatException('Invalid time format: $timeString');
    }

    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);

    return hours * 60 + minutes;
  }

  /// Calculate distance between two coordinates using Haversine formula
  static double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLon = _degreesToRadians(lon2 - lon1);

    final a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final distanceKm = _earthRadiusKm * c;

    return distanceKm * 1000; // Convert to meters
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Get current distance without starting tracking
  Future<DistanceUpdate?> getCurrentDistance({
    User? user,
    Church? church,
  }) async {
    if (church != null) {
      initializeWithChurch(church);
    } else if (_currentChurch == null) {
      await initialize(user: user);
    }

    final location = await _geolocationService.getCurrentLocation();
    if (location == null) return null;

    final distanceMeters = calculateDistance(
      location.latitude,
      location.longitude,
      _currentChurch!.latitude!,
      _currentChurch!.longitude!,
    );

    return DistanceUpdate(
      distanceMeters: distanceMeters,
      userLocation: location,
      churchLocation: ChurchLocation(
        latitude: _currentChurch!.latitude!,
        longitude: _currentChurch!.longitude!,
        name: _currentChurch!.name,
        address: _currentChurch!.address,
      ),
      timestamp: DateTime.now(),
      isWithinGeofence: distanceMeters <= _currentChurch!.geofenceRadius,
      geofenceRadius: _currentChurch!.geofenceRadius.toDouble(),
    );
  }

  /// Dispose resources
  void dispose() {
    stopTracking();
    _distanceController.close();
    _locationController.close();
    _notificationController.close();
  }
}

/// Distance update data model
class DistanceUpdate {
  final double distanceMeters;
  final LocationData userLocation;
  final ChurchLocation churchLocation;
  final DateTime timestamp;
  final bool isWithinGeofence;
  final double geofenceRadius;

  DistanceUpdate({
    required this.distanceMeters,
    required this.userLocation,
    required this.churchLocation,
    required this.timestamp,
    required this.isWithinGeofence,
    required this.geofenceRadius,
  });

  /// Get formatted distance string
  String get formattedDistance {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m';
    } else {
      final km = distanceMeters / 1000;
      return '${km.toStringAsFixed(1)}km';
    }
  }

  /// Get distance status message
  String get statusMessage {
    if (isWithinGeofence) {
      return 'You are at church';
    } else if (distanceMeters < 100) {
      return 'Very close to church';
    } else if (distanceMeters < 500) {
      return 'Approaching church';
    } else if (distanceMeters < 1000) {
      return 'Near church';
    } else {
      return 'Distance to church';
    }
  }

  /// Get estimated walking time (assuming 5 km/h walking speed)
  String get estimatedWalkingTime {
    if (distanceMeters < 50) return 'You\'re here!';

    final walkingSpeedKmh = 5.0;
    final walkingSpeedMs = walkingSpeedKmh * 1000 / 3600; // m/s
    final timeSeconds = distanceMeters / walkingSpeedMs;

    if (timeSeconds < 60) {
      return '${timeSeconds.round()} sec walk';
    } else if (timeSeconds < 3600) {
      final minutes = (timeSeconds / 60).round();
      return '$minutes min walk';
    } else {
      final hours = (timeSeconds / 3600).round();
      return '$hours hr walk';
    }
  }
}

/// Church location data model
class ChurchLocation {
  final double latitude;
  final double longitude;
  final String name;
  final String address;

  ChurchLocation({
    required this.latitude,
    required this.longitude,
    required this.name,
    required this.address,
  });
}

/// Attendance notification data model
class AttendanceNotification {
  final String message;
  final AttendanceNotificationType type;
  final DateTime timestamp;
  final String? serviceName;

  AttendanceNotification({
    required this.message,
    required this.type,
    required this.timestamp,
    this.serviceName,
  });

  factory AttendanceNotification.success(
    String message, {
    String? serviceName,
  }) {
    return AttendanceNotification(
      message: message,
      type: AttendanceNotificationType.success,
      timestamp: DateTime.now(),
      serviceName: serviceName,
    );
  }

  factory AttendanceNotification.error(String message) {
    return AttendanceNotification(
      message: message,
      type: AttendanceNotificationType.error,
      timestamp: DateTime.now(),
    );
  }

  factory AttendanceNotification.info(String message) {
    return AttendanceNotification(
      message: message,
      type: AttendanceNotificationType.info,
      timestamp: DateTime.now(),
    );
  }
}

enum AttendanceNotificationType { success, error, info }
