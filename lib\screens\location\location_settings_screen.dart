import 'package:flutter/material.dart';

import 'package:flockin_app/utils/colors.dart';
import 'package:flockin_app/utils/constants.dart';

class LocationSettingsScreen extends StatefulWidget {
  const LocationSettingsScreen({super.key});

  @override
  State<LocationSettingsScreen> createState() => _LocationSettingsScreenState();
}

class _LocationSettingsScreenState extends State<LocationSettingsScreen> {
  bool _automaticAttendance = true;
  bool _backgroundLocation = false;
  bool _highAccuracy = true;
  double _geofenceRadius = 100.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back),
                      color: AppColors.textPrimary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Location Services',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                  ],
                ),
              ),

              // Settings Content
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                  ),
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 24),
                    _buildSettingsSection('Automatic Attendance', [
                      _buildSwitchTile(
                        'Enable Automatic Check-in',
                        'Automatically check in when you arrive at church',
                        _automaticAttendance,
                        (value) => setState(() => _automaticAttendance = value),
                      ),
                      _buildSwitchTile(
                        'Background Location',
                        'Allow location access when app is closed',
                        _backgroundLocation,
                        (value) => setState(() => _backgroundLocation = value),
                      ),
                      _buildSwitchTile(
                        'High Accuracy Mode',
                        'Use GPS for more precise location',
                        _highAccuracy,
                        (value) => setState(() => _highAccuracy = value),
                      ),
                    ]),
                    const SizedBox(height: 24),
                    _buildSettingsSection('Geofence Settings', [
                      _buildSliderTile(
                        'Detection Radius',
                        'Distance from church for automatic check-in',
                        _geofenceRadius,
                        50.0,
                        500.0,
                        (value) => setState(() => _geofenceRadius = value),
                      ),
                    ]),
                    const SizedBox(height: 24),
                    _buildPermissionsSection(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gradientStart.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppColors.gradientStart.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.gradientStart, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Location services help automatically track your church attendance. Your location data is only used for attendance purposes.',
              style: TextStyle(
                color: AppColors.textPrimary.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 12),
          child: Text(
            title,
            style: TextStyle(
              color: AppColors.textPrimary.withValues(alpha: 0.7),
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: Column(children: items),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textPrimary.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.gradientStart,
      activeTrackColor: AppColors.gradientStart.withValues(alpha: 0.3),
      inactiveThumbColor: Colors.grey.shade400,
      inactiveTrackColor: Colors.grey.shade300,
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.textPrimary.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  divisions: ((max - min) / 10).round(),
                  onChanged: onChanged,
                  activeColor: AppColors.gradientStart,
                  inactiveColor: Colors.grey.shade300,
                ),
              ),
              Text(
                '${value.round()}m',
                style: TextStyle(
                  color: AppColors.textPrimary.withValues(alpha: 0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 12),
          child: Text(
            'Permissions',
            style: TextStyle(
              color: AppColors.textPrimary.withValues(alpha: 0.7),
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: ListTile(
            leading: Icon(
              Icons.settings,
              color: AppColors.textPrimary,
              size: 24,
            ),
            title: const Text(
              'Manage Permissions',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: const Text(
              'Open system settings to manage location permissions',
              style: TextStyle(color: AppColors.textPrimary, fontSize: 14),
            ),
            trailing: Icon(
              Icons.chevron_right,
              color: AppColors.textPrimary.withValues(alpha: 0.5),
            ),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening system settings...')),
              );
            },
          ),
        ),
      ],
    );
  }
}
