import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseTestScreen extends StatefulWidget {
  const FirebaseTestScreen({super.key});

  @override
  State<FirebaseTestScreen> createState() => _FirebaseTestScreenState();
}

class _FirebaseTestScreenState extends State<FirebaseTestScreen> {
  String _status = 'Testing Firebase connection...';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _testFirebaseConnection();
  }

  Future<void> _testFirebaseConnection() async {
    try {
      // Test Firebase Core
      final app = Firebase.app();
      setState(() {
        _status =
            'Firebase Core: ✅ Connected\nProject: ${app.options.projectId}\n';
      });

      // Test Firebase Auth
      setState(() {
        _status += 'Firebase Auth: ✅ Available\n';
      });

      // Test Firestore
      final firestore = FirebaseFirestore.instance;

      setState(() {
        _status += 'Firestore: ✅ Connected\n';
      });

      // Test authentication (create a test user)
      try {
        final auth = FirebaseAuth.instance;

        // Try to create a test user
        final testEmail = '<EMAIL>';
        final testPassword = 'testpassword123';

        UserCredential? userCredential;
        try {
          // Try to sign in first
          userCredential = await auth.signInWithEmailAndPassword(
            email: testEmail,
            password: testPassword,
          );
        } catch (e) {
          // If sign in fails, create the user
          userCredential = await auth.createUserWithEmailAndPassword(
            email: testEmail,
            password: testPassword,
          );
        }

        if (userCredential.user != null) {
          setState(() {
            _status += 'Auth Test: ✅ User authentication working\n';
          });

          // Now test Firestore write with authenticated user
          await firestore.collection('test').doc('connection_test').set({
            'timestamp': FieldValue.serverTimestamp(),
            'message': 'Firebase connection test successful!',
            'userId': userCredential.user!.uid,
          });

          setState(() {
            _status += 'Firestore Write: ✅ Database writable with auth\n';
            _status += '\n🎉 All Firebase services are working!';
            _isLoading = false;
          });

          // Sign out the test user
          await auth.signOut();
        }
      } catch (authError) {
        setState(() {
          _status += 'Auth/Firestore Test: ⚠️ ${authError.toString()}\n';
          _status += '\n✅ Firebase Core and Auth are working!\n';
          _status += '📝 Note: Firestore requires authentication for writes';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _status += '\n❌ Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Connection Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isLoading) const Center(child: CircularProgressIndicator()),
            const SizedBox(height: 20),
            Text(
              _status,
              style: const TextStyle(fontSize: 16, fontFamily: 'monospace'),
            ),
            const SizedBox(height: 20),
            if (!_isLoading)
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _status = 'Testing Firebase connection...';
                  });
                  _testFirebaseConnection();
                },
                child: const Text('Test Again'),
              ),
          ],
        ),
      ),
    );
  }
}
