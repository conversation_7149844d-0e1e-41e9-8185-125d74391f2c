import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:flockin_app/utils/colors.dart';
import 'package:flockin_app/widgets/common/gradient_container.dart';
import 'package:flockin_app/services/attendance_service.dart';
import 'package:flockin_app/models/attendance.dart';

class RecentAttendanceCard extends StatefulWidget {
  final VoidCallback? onViewAllPressed;
  final VoidCallback? onRefresh;

  const RecentAttendanceCard({
    super.key,
    this.onViewAllPressed,
    this.onRefresh,
  });

  @override
  State<RecentAttendanceCard> createState() => _RecentAttendanceCardState();
}

class _RecentAttendanceCardState extends State<RecentAttendanceCard> {
  final AttendanceService _attendanceService = AttendanceService();
  List<Attendance> _recentAttendance = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadRecentAttendance();
  }

  Future<void> _loadRecentAttendance() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final attendance = await _attendanceService.getMyAttendanceHistory(
        limit: 3,
      );

      setState(() {
        _recentAttendance = attendance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refresh() async {
    await _loadRecentAttendance();
    if (widget.onRefresh != null) {
      widget.onRefresh!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      borderRadius: BorderRadius.circular(24),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Attendance',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w700,
                ),
              ),
              GestureDetector(
                onTap: widget.onViewAllPressed,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'View All',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildContent(),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          children: [
            Text(
              'Failed to load attendance history',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 8),
            TextButton(onPressed: _refresh, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_recentAttendance.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Text(
            'No attendance records found',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ),
      );
    }

    return Column(
      children: _recentAttendance.map((attendance) {
        return _buildAttendanceItem(attendance);
      }).toList(),
    );
  }

  Widget _buildAttendanceItem(Attendance attendance) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final timeFormat = DateFormat('HH:mm a');

    return Builder(
      builder: (context) {
        return _buildAttendanceItemContent(
          context,
          attendance.serviceType,
          dateFormat.format(attendance.checkInTime),
          _getStatusDisplay(attendance.status),
          timeFormat.format(attendance.checkInTime),
        );
      },
    );
  }

  String _getStatusDisplay(String status) {
    switch (status.toLowerCase()) {
      case 'present':
        return 'Present';
      case 'late':
        return 'Late';
      case 'absent':
        return 'Absent';
      default:
        return 'Present';
    }
  }

  Widget _buildAttendanceItemContent(
    BuildContext context,
    String service,
    String date,
    String status,
    String time,
  ) {
    Color statusColor;
    IconData statusIcon;
    LinearGradient statusGradient;

    switch (status.toLowerCase()) {
      case 'present':
        statusColor = AppColors.present;
        statusIcon = Icons.check_circle;
        statusGradient = AppColors.checkInGradient;
        break;
      case 'absent':
        statusColor = AppColors.absent;
        statusIcon = Icons.cancel;
        statusGradient = AppColors.checkOutGradient;
        break;
      default:
        statusColor = AppColors.warning;
        statusIcon = Icons.access_time;
        statusGradient = AppColors.currentStreakGradient;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: statusGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: statusColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(statusIcon, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  service,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  date,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: AppColors.textMuted),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  gradient: statusGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (time.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
