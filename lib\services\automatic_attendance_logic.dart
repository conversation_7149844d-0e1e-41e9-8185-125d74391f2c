import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:flockin_mobile/services/attendance_service.dart';
import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/services/geolocation_service.dart';

/// Core logic for automatic attendance management
class AutomaticAttendanceLogic {
  static const String _pendingCheckInsKey = 'pending_check_ins';
  static const String _lastGeofenceCheckKey = 'last_geofence_check';
  static const String _consecutiveFailuresKey = 'consecutive_failures';
  static const String _lastSuccessfulCheckInKey = 'last_successful_checkin';

  static const Duration _geofenceCheckCooldown = Duration(minutes: 2);
  static const Duration _retryDelay = Duration(minutes: 5);
  static const int _maxConsecutiveFailures = 3;
  static const double _minimumAccuracyThreshold = 100.0; // meters

  final AttendanceService _attendanceService;
  final GeolocationService _geolocationService;
  final AutomaticAttendanceService _automaticAttendanceService;

  AutomaticAttendanceLogic({
    required AttendanceService attendanceService,
    required GeolocationService geolocationService,
    required AutomaticAttendanceService automaticAttendanceService,
  }) : _attendanceService = attendanceService,
       _geolocationService = geolocationService,
       _automaticAttendanceService = automaticAttendanceService;

  /// Main method to check and handle automatic attendance
  Future<AttendanceCheckResult> checkAndHandleAttendance() async {
    try {
      // Check if we should skip this check due to cooldown
      if (await _shouldSkipCheck()) {
        return AttendanceCheckResult.skipped('Cooldown period active');
      }

      // Get current location
      final location = await _geolocationService.getCurrentLocation();
      if (location == null) {
        return AttendanceCheckResult.failed('Unable to get current location');
      }

      // Check location accuracy
      if (location.accuracy > _minimumAccuracyThreshold) {
        if (kDebugMode) {
          print('Location accuracy too low: ${location.accuracy}m');
        }
        return AttendanceCheckResult.failed('Location accuracy too low');
      }

      // Get current attendance status
      final currentStatus = await _automaticAttendanceService
          .getCurrentStatus();

      // Validate location for check-in
      final validation = await _attendanceService.validateLocationForCheckIn();
      if (validation == null) {
        await _recordFailure();
        return AttendanceCheckResult.failed('Location validation failed');
      }

      await _updateLastGeofenceCheck();

      // Handle different scenarios
      if (validation.canCheckIn && !currentStatus.isPresent) {
        return await _handleCheckIn(location, validation);
      } else if (!validation.canCheckIn && currentStatus.isPresent) {
        return await _handleCheckOut(location);
      } else if (validation.canCheckIn && currentStatus.isPresent) {
        return AttendanceCheckResult.noAction('Already checked in');
      } else {
        return AttendanceCheckResult.noAction('Not in geofence area');
      }
    } catch (e) {
      await _recordFailure();
      if (kDebugMode) {
        print('Error in automatic attendance check: $e');
      }
      return AttendanceCheckResult.failed('Unexpected error: ${e.toString()}');
    }
  }

  /// Handle automatic check-in
  Future<AttendanceCheckResult> _handleCheckIn(
    LocationData location,
    LocationValidationResult validation,
  ) async {
    try {
      // Check if we already have a recent successful check-in
      if (await _hasRecentCheckIn()) {
        return AttendanceCheckResult.noAction('Recent check-in exists');
      }

      // Attempt check-in
      final result = await _attendanceService.checkIn();
      if (result != null) {
        // Success - update status
        final status = AttendanceStatus.present(
          checkInTime: DateTime.now(),
          location: location,
        );

        await _automaticAttendanceService.updateAttendanceStatus(status);
        await _recordSuccessfulCheckIn();
        await _clearFailures();

        if (kDebugMode) {
          print('Automatic check-in successful');
        }

        return AttendanceCheckResult.success(
          'Automatically checked in',
          checkInTime: DateTime.now(),
        );
      } else {
        throw Exception('Check-in returned null result');
      }
    } catch (e) {
      // Handle offline scenario
      await _savePendingCheckIn(location);
      await _recordFailure();

      if (kDebugMode) {
        print('Check-in failed, saved as pending: $e');
      }

      return AttendanceCheckResult.pending(
        'Check-in saved for when connection is restored',
      );
    }
  }

  /// Handle automatic check-out (when user leaves geofence)
  Future<AttendanceCheckResult> _handleCheckOut(LocationData location) async {
    try {
      // Update status to absent
      final status = AttendanceStatus.absent();
      await _automaticAttendanceService.updateAttendanceStatus(status);

      if (kDebugMode) {
        print('User left geofence - marked as absent');
      }

      return AttendanceCheckResult.success(
        'Marked as absent - left church area',
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error handling check-out: $e');
      }
      return AttendanceCheckResult.failed('Failed to update status');
    }
  }

  /// Process pending check-ins when connection is restored
  Future<List<AttendanceCheckResult>> processPendingCheckIns() async {
    final results = <AttendanceCheckResult>[];

    try {
      final pendingCheckIns = await _getPendingCheckIns();

      for (final pendingCheckIn in pendingCheckIns) {
        try {
          final timestamp = DateTime.parse(pendingCheckIn['timestamp']);

          // Check if the pending check-in is still valid (not too old)
          if (DateTime.now().difference(timestamp).inHours > 24) {
            continue; // Skip old pending check-ins
          }

          // Attempt the check-in
          final result = await _attendanceService.checkIn();
          if (result != null) {
            results.add(
              AttendanceCheckResult.success(
                'Pending check-in processed successfully',
                checkInTime: timestamp,
              ),
            );
          }
        } catch (e) {
          results.add(
            AttendanceCheckResult.failed(
              'Failed to process pending check-in: ${e.toString()}',
            ),
          );
        }
      }

      // Clear processed pending check-ins
      await _clearPendingCheckIns();
    } catch (e) {
      if (kDebugMode) {
        print('Error processing pending check-ins: $e');
      }
    }

    return results;
  }

  /// Check if we should skip the current check due to cooldown
  Future<bool> _shouldSkipCheck() async {
    final prefs = await SharedPreferences.getInstance();
    final lastCheckString = prefs.getString(_lastGeofenceCheckKey);

    if (lastCheckString != null) {
      final lastCheck = DateTime.parse(lastCheckString);
      final timeSinceLastCheck = DateTime.now().difference(lastCheck);

      if (timeSinceLastCheck < _geofenceCheckCooldown) {
        return true;
      }
    }

    // Check consecutive failures
    final consecutiveFailures = prefs.getInt(_consecutiveFailuresKey) ?? 0;
    if (consecutiveFailures >= _maxConsecutiveFailures) {
      final lastFailureTime = lastCheckString != null
          ? DateTime.parse(lastCheckString)
          : DateTime.now();

      if (DateTime.now().difference(lastFailureTime) < _retryDelay) {
        return true;
      }
    }

    return false;
  }

  /// Update last geofence check timestamp
  Future<void> _updateLastGeofenceCheck() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      _lastGeofenceCheckKey,
      DateTime.now().toIso8601String(),
    );
  }

  /// Record a failure
  Future<void> _recordFailure() async {
    final prefs = await SharedPreferences.getInstance();
    final currentFailures = prefs.getInt(_consecutiveFailuresKey) ?? 0;
    await prefs.setInt(_consecutiveFailuresKey, currentFailures + 1);
  }

  /// Clear failure count
  Future<void> _clearFailures() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_consecutiveFailuresKey);
  }

  /// Record successful check-in
  Future<void> _recordSuccessfulCheckIn() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      _lastSuccessfulCheckInKey,
      DateTime.now().toIso8601String(),
    );
  }

  /// Check if there's a recent successful check-in
  Future<bool> _hasRecentCheckIn() async {
    final prefs = await SharedPreferences.getInstance();
    final lastCheckInString = prefs.getString(_lastSuccessfulCheckInKey);

    if (lastCheckInString != null) {
      final lastCheckIn = DateTime.parse(lastCheckInString);
      final timeSinceLastCheckIn = DateTime.now().difference(lastCheckIn);

      // Consider check-in recent if within the last hour
      return timeSinceLastCheckIn.inHours < 1;
    }

    return false;
  }

  /// Save pending check-in for offline scenarios
  Future<void> _savePendingCheckIn(LocationData location) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingCheckIns = await _getPendingCheckIns();

    pendingCheckIns.add({
      'location': location.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    });

    await prefs.setString(_pendingCheckInsKey, jsonEncode(pendingCheckIns));
  }

  /// Get pending check-ins
  Future<List<Map<String, dynamic>>> _getPendingCheckIns() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingCheckInsString = prefs.getString(_pendingCheckInsKey);

    if (pendingCheckInsString != null) {
      try {
        final List<dynamic> decoded = jsonDecode(pendingCheckInsString);
        return decoded.cast<Map<String, dynamic>>();
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing pending check-ins: $e');
        }
      }
    }

    return [];
  }

  /// Clear pending check-ins
  Future<void> _clearPendingCheckIns() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_pendingCheckInsKey);
  }
}

/// Result of an attendance check operation
class AttendanceCheckResult {
  final AttendanceCheckStatus status;
  final String message;
  final DateTime? checkInTime;

  AttendanceCheckResult._({
    required this.status,
    required this.message,
    this.checkInTime,
  });

  factory AttendanceCheckResult.success(
    String message, {
    DateTime? checkInTime,
  }) {
    return AttendanceCheckResult._(
      status: AttendanceCheckStatus.success,
      message: message,
      checkInTime: checkInTime,
    );
  }

  factory AttendanceCheckResult.failed(String message) {
    return AttendanceCheckResult._(
      status: AttendanceCheckStatus.failed,
      message: message,
    );
  }

  factory AttendanceCheckResult.pending(String message) {
    return AttendanceCheckResult._(
      status: AttendanceCheckStatus.pending,
      message: message,
    );
  }

  factory AttendanceCheckResult.noAction(String message) {
    return AttendanceCheckResult._(
      status: AttendanceCheckStatus.noAction,
      message: message,
    );
  }

  factory AttendanceCheckResult.skipped(String message) {
    return AttendanceCheckResult._(
      status: AttendanceCheckStatus.skipped,
      message: message,
    );
  }

  bool get isSuccess => status == AttendanceCheckStatus.success;
  bool get isFailed => status == AttendanceCheckStatus.failed;
  bool get isPending => status == AttendanceCheckStatus.pending;
  bool get isNoAction => status == AttendanceCheckStatus.noAction;
  bool get isSkipped => status == AttendanceCheckStatus.skipped;
}

enum AttendanceCheckStatus { success, failed, pending, noAction, skipped }
