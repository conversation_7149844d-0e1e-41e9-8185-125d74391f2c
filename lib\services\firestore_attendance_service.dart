import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/attendance.dart';

class FirestoreAttendanceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Check in attendance
  Future<bool> checkIn({
    required String memberId,
    required String churchId,
    required String serviceType,
    required double latitude,
    required double longitude,
    required double locationAccuracy,
    String? notes,
  }) async {
    try {
      final attendanceData = {
        'memberId': memberId,
        'churchId': churchId,
        'checkInTime': FieldValue.serverTimestamp(),
        'status': 'present',
        'serviceType': serviceType,
        'latitude': latitude,
        'longitude': longitude,
        'locationAccuracy': locationAccuracy,
        'notes': notes,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('attendance').add(attendanceData);
      debugPrint('✅ Check-in successful');
      return true;
    } catch (e) {
      debugPrint('❌ Check-in error: $e');
      return false;
    }
  }

  // Check out attendance
  Future<bool> checkOut(String attendanceId) async {
    try {
      await _firestore.collection('attendance').doc(attendanceId).update({
        'checkOutTime': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      debugPrint('✅ Check-out successful');
      return true;
    } catch (e) {
      debugPrint('❌ Check-out error: $e');
      return false;
    }
  }

  // Get attendance history for a member
  Future<List<Attendance>> getAttendanceHistory(String memberId, {int limit = 50}) async {
    try {
      final querySnapshot = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .orderBy('checkInTime', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        
        // Convert Firestore Timestamps to DateTime
        if (data['checkInTime'] is Timestamp) {
          data['checkInTime'] = (data['checkInTime'] as Timestamp).toDate().toIso8601String();
        }
        if (data['checkOutTime'] is Timestamp) {
          data['checkOutTime'] = (data['checkOutTime'] as Timestamp).toDate().toIso8601String();
        }
        
        return Attendance.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching attendance history: $e');
      return [];
    }
  }

  // Get current active attendance (not checked out)
  Future<Attendance?> getCurrentAttendance(String memberId) async {
    try {
      final querySnapshot = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .where('checkOutTime', isNull: true)
          .orderBy('checkInTime', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final data = doc.data();
        data['id'] = doc.id;
        
        // Convert Firestore Timestamps to DateTime
        if (data['checkInTime'] is Timestamp) {
          data['checkInTime'] = (data['checkInTime'] as Timestamp).toDate().toIso8601String();
        }
        
        return Attendance.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error fetching current attendance: $e');
      return null;
    }
  }

  // Get attendance statistics
  Future<AttendanceStats> getAttendanceStats(String memberId) async {
    try {
      final now = DateTime.now();
      final thisMonthStart = DateTime(now.year, now.month, 1);
      final lastMonthStart = DateTime(now.year, now.month - 1, 1);
      final lastMonthEnd = DateTime(now.year, now.month, 0);

      // Get all attendance records for the member
      final allAttendanceQuery = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .get();

      final totalAttendance = allAttendanceQuery.docs.length;

      // Get this month's attendance
      final thisMonthQuery = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .where('checkInTime', isGreaterThanOrEqualTo: Timestamp.fromDate(thisMonthStart))
          .get();

      final thisMonthAttendance = thisMonthQuery.docs.length;

      // Get last month's attendance
      final lastMonthQuery = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .where('checkInTime', isGreaterThanOrEqualTo: Timestamp.fromDate(lastMonthStart))
          .where('checkInTime', isLessThanOrEqualTo: Timestamp.fromDate(lastMonthEnd))
          .get();

      final lastMonthAttendance = lastMonthQuery.docs.length;

      // Calculate current streak (simplified - you might want to make this more sophisticated)
      int currentStreak = 0;
      final recentAttendance = await _firestore
          .collection('attendance')
          .where('memberId', isEqualTo: memberId)
          .orderBy('checkInTime', descending: true)
          .limit(10)
          .get();

      // Simple streak calculation - consecutive weeks with attendance
      // You can make this more sophisticated based on your requirements
      if (recentAttendance.docs.isNotEmpty) {
        currentStreak = 1; // At least 1 if they have any recent attendance
      }

      // For now, assume total services is roughly totalAttendance * 1.2 (80% attendance rate)
      final estimatedTotalServices = (totalAttendance * 1.25).round();
      final attendanceRate = estimatedTotalServices > 0 ? totalAttendance / estimatedTotalServices : 0.0;

      return AttendanceStats(
        totalServices: estimatedTotalServices,
        servicesAttended: totalAttendance,
        currentStreak: currentStreak,
        attendanceRate: attendanceRate,
        thisMonthAttendance: thisMonthAttendance,
        lastMonthAttendance: lastMonthAttendance,
      );
    } catch (e) {
      debugPrint('❌ Error calculating attendance stats: $e');
      return AttendanceStats(
        totalServices: 0,
        servicesAttended: 0,
        currentStreak: 0,
        attendanceRate: 0.0,
        thisMonthAttendance: 0,
        lastMonthAttendance: 0,
      );
    }
  }

  // Get live attendance for a church
  Future<List<Attendance>> getLiveAttendance(String churchId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      final querySnapshot = await _firestore
          .collection('attendance')
          .where('churchId', isEqualTo: churchId)
          .where('checkInTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('checkOutTime', isNull: true)
          .orderBy('checkInTime', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        
        // Convert Firestore Timestamps to DateTime
        if (data['checkInTime'] is Timestamp) {
          data['checkInTime'] = (data['checkInTime'] as Timestamp).toDate().toIso8601String();
        }
        
        return Attendance.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('❌ Error fetching live attendance: $e');
      return [];
    }
  }

  // Stream live attendance updates
  Stream<List<Attendance>> streamLiveAttendance(String churchId) {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);

    return _firestore
        .collection('attendance')
        .where('churchId', isEqualTo: churchId)
        .where('checkInTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .orderBy('checkInTime', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        
        // Convert Firestore Timestamps to DateTime
        if (data['checkInTime'] is Timestamp) {
          data['checkInTime'] = (data['checkInTime'] as Timestamp).toDate().toIso8601String();
        }
        if (data['checkOutTime'] is Timestamp) {
          data['checkOutTime'] = (data['checkOutTime'] as Timestamp).toDate().toIso8601String();
        }
        
        return Attendance.fromJson(data);
      }).toList();
    });
  }
}
