import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flockin_mobile/models/user_model.dart';
import 'package:flockin_mobile/services/auth_service.dart';
import 'package:flockin_mobile/services/automatic_attendance_service.dart';
import 'package:flockin_mobile/services/church_service.dart';
import 'package:flockin_mobile/services/distance_tracker_service.dart';
import 'package:flockin_mobile/utils/colors.dart';
import 'package:flockin_mobile/utils/constants.dart';

class LiveDistanceWidget extends StatefulWidget {
  final bool autoStart;
  final VoidCallback? onTap;
  final bool showWalkingTime;
  final bool compact;
  final Church? church; // Optional church data to avoid duplicate API calls
  final AutomaticAttendanceService? attendanceService;

  const LiveDistanceWidget({
    super.key,
    this.autoStart = true,
    this.onTap,
    this.showWalkingTime = true,
    this.compact = false,
    this.church,
    this.attendanceService,
  });

  @override
  State<LiveDistanceWidget> createState() => _LiveDistanceWidgetState();
}

class _LiveDistanceWidgetState extends State<LiveDistanceWidget>
    with TickerProviderStateMixin {
  late DistanceTrackerService _distanceTracker;
  StreamSubscription<DistanceUpdate>? _distanceSubscription;
  StreamSubscription<AttendanceNotification>? _notificationSubscription;

  DistanceUpdate? _currentDistance;
  bool _isLoading = true;
  String? _errorMessage;

  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _distanceTracker = DistanceTrackerService();

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeOutCubic),
    );

    if (widget.autoStart) {
      _initializeAndStartTracking();
    }
  }

  @override
  void dispose() {
    _distanceSubscription?.cancel();
    _notificationSubscription?.cancel();
    _distanceTracker.dispose();
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  Future<void> _initializeAndStartTracking() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get current user from AuthService
      final authService = Provider.of<AuthService>(context, listen: false);
      final user = authService.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _distanceTracker.initialize(user: user);
      await _startTracking(user);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  Future<void> _startTracking(User user) async {
    try {
      await _distanceTracker.startTracking(
        user: user,
        church: widget.church,
        attendanceService: widget.attendanceService,
      );

      _distanceSubscription = _distanceTracker.distanceStream.listen(
        (distanceUpdate) {
          if (mounted) {
            setState(() {
              _currentDistance = distanceUpdate;
              _isLoading = false;
              _errorMessage = null;
            });

            // Trigger animations
            _progressController.forward();
            if (distanceUpdate.isWithinGeofence) {
              _pulseController.repeat(reverse: true);
            } else {
              _pulseController.stop();
            }
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _errorMessage = error.toString();
              _isLoading = false;
            });
          }
        },
      );

      // Listen for attendance notifications
      _notificationSubscription = _distanceTracker.notificationStream.listen((
        notification,
      ) {
        if (mounted) {
          _showAttendanceNotification(notification);
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compact) {
      return _buildCompactWidget();
    }
    return _buildFullWidget();
  }

  Widget _buildFullWidget() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: _getGradientForDistance(),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [
            BoxShadow(
              color: _getGradientForDistance().colors.first.withValues(
                alpha: 0.3,
              ),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _currentDistance?.isWithinGeofence == true
                          ? _pulseAnimation.value
                          : 1.0,
                      child: Icon(
                        _getIconForDistance(),
                        color: Colors.white,
                        size: 28,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getTitle(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSubtitle(),
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_currentDistance != null) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _progressAnimation.value,
                            child: Text(
                              _currentDistance!.formattedDistance,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.w900,
                              ),
                            ),
                          );
                        },
                      ),
                      if (widget.showWalkingTime) ...[
                        const SizedBox(height: 4),
                        Text(
                          _currentDistance!.estimatedWalkingTime,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
            if (_isLoading) ...[
              const SizedBox(height: 16),
              const LinearProgressIndicator(
                backgroundColor: Colors.white24,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getColorForDistance(),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getIconForDistance(), color: Colors.white, size: 16),
          const SizedBox(width: 6),
          if (_currentDistance != null)
            Text(
              _currentDistance!.formattedDistance,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            )
          else if (_isLoading)
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            const Text(
              'Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  String _getTitle() {
    if (_isLoading) return 'Locating...';
    if (_errorMessage != null) return 'Location Error';
    if (_currentDistance == null) return 'Distance to Church';
    return _currentDistance!.statusMessage;
  }

  String _getSubtitle() {
    if (_isLoading) return 'Getting your location and calculating distance';
    if (_errorMessage != null) return _errorMessage!;
    if (_currentDistance == null) return 'Tap to start tracking';
    return 'Distance to ${_currentDistance!.churchLocation.name}';
  }

  IconData _getIconForDistance() {
    if (_isLoading || _currentDistance == null) return Icons.location_searching;
    if (_errorMessage != null) return Icons.location_off;
    if (_currentDistance!.isWithinGeofence) return Icons.location_on;
    if (_currentDistance!.distanceMeters < 500) return Icons.near_me;
    return Icons.navigation;
  }

  LinearGradient _getGradientForDistance() {
    if (_isLoading || _currentDistance == null) {
      return const LinearGradient(
        colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
      );
    }
    if (_errorMessage != null) {
      return const LinearGradient(
        colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
      );
    }
    if (_currentDistance!.isWithinGeofence) {
      return const LinearGradient(
        colors: [Color(0xFF10B981), Color(0xFF059669)],
      );
    }
    if (_currentDistance!.distanceMeters < 500) {
      return const LinearGradient(
        colors: [Color(0xFFF59E0B), Color(0xFFD97706)],
      );
    }
    return AppColors.primaryGradient;
  }

  Color _getColorForDistance() {
    if (_isLoading || _currentDistance == null) return const Color(0xFF6366F1);
    if (_errorMessage != null) return const Color(0xFFEF4444);
    if (_currentDistance!.isWithinGeofence) return const Color(0xFF10B981);
    if (_currentDistance!.distanceMeters < 500) return const Color(0xFFF59E0B);
    return AppColors.gradientStart;
  }

  /// Show attendance notification to user
  void _showAttendanceNotification(AttendanceNotification notification) {
    Color backgroundColor;
    IconData icon;

    switch (notification.type) {
      case AttendanceNotificationType.success:
        backgroundColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case AttendanceNotificationType.error:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case AttendanceNotificationType.info:
        backgroundColor = Colors.blue;
        icon = Icons.info;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                notification.message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
