import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flockin_mobile/services/geolocation_service.dart';
import 'package:flockin_mobile/utils/constants.dart';
import 'package:flockin_mobile/models/attendance.dart';

class CheckInRequest {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final String? attendanceSessionId;
  final String? method;
  final String? deviceInfo;

  CheckInRequest({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.attendanceSessionId,
    this.method = 'automatic',
    this.deviceInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      if (accuracy != null) 'accuracy': accuracy,
      if (attendanceSessionId != null)
        'attendanceSessionId': attendanceSessionId,
      'method': method,
      if (deviceInfo != null) 'deviceInfo': deviceInfo,
    };
  }
}

class CheckInResponse {
  final String message;
  final AttendanceRecord attendance;

  CheckInResponse({required this.message, required this.attendance});

  factory CheckInResponse.fromJson(Map<String, dynamic> json) {
    return CheckInResponse(
      message: json['message'],
      attendance: AttendanceRecord.fromJson(json['attendance']),
    );
  }
}

class AttendanceRecord {
  final String id;
  final DateTime checkInTime;
  final DateTime? checkOutTime;
  final String method;
  final double? distance;
  final double? accuracy;
  final bool isVerified;
  final bool isFlagged;
  final int? duration;

  AttendanceRecord({
    required this.id,
    required this.checkInTime,
    this.checkOutTime,
    required this.method,
    this.distance,
    this.accuracy,
    required this.isVerified,
    required this.isFlagged,
    this.duration,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) {
    return AttendanceRecord(
      id: json['id'],
      checkInTime: DateTime.parse(json['checkInTime']),
      checkOutTime: json['checkOutTime'] != null
          ? DateTime.parse(json['checkOutTime'])
          : null,
      method: json['method'],
      distance: json['distance']?.toDouble(),
      accuracy: json['accuracy']?.toDouble(),
      isVerified: json['isVerified'] ?? true,
      isFlagged: json['isFlagged'] ?? false,
      duration: json['duration'],
    );
  }
}

class LocationValidationResult {
  final bool canCheckIn;
  final double distance;
  final String message;
  final double suspicionScore;

  LocationValidationResult({
    required this.canCheckIn,
    required this.distance,
    required this.message,
    required this.suspicionScore,
  });

  factory LocationValidationResult.fromJson(Map<String, dynamic> json) {
    return LocationValidationResult(
      canCheckIn: json['canCheckIn'],
      distance: json['distance'].toDouble(),
      message: json['message'],
      suspicionScore: json['suspicionScore'].toDouble(),
    );
  }
}

class AttendanceService {
  final GeolocationService _geolocationService = GeolocationService();
  static const _storage = FlutterSecureStorage();
  static const _tokenKey = 'auth_token';

  /// Get authentication token from storage
  Future<String?> _getAuthToken() async {
    return await _storage.read(key: _tokenKey);
  }

  /// Get common headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Check in to church service
  Future<CheckInResponse?> checkIn({
    String? attendanceSessionId,
    String? deviceInfo,
  }) async {
    try {
      // Get current location
      final locationData = await _geolocationService.getCurrentLocation();
      if (locationData == null) {
        throw Exception('Unable to get current location');
      }

      // Prepare request
      final request = CheckInRequest(
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        attendanceSessionId: attendanceSessionId,
        deviceInfo: deviceInfo ?? await _getDeviceInfo(),
      );

      // Make API call
      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/attendance/checkin'),
        headers: await _getHeaders(),
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 201) {
        return CheckInResponse.fromJson(jsonDecode(response.body));
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Check-in failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Check-in error: $e');
      }
      rethrow;
    }
  }

  /// Check out from church service
  Future<CheckInResponse?> checkOut({String? attendanceRecordId}) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConstants.baseUrl}/attendance/checkout'),
        headers: await _getHeaders(),
        body: jsonEncode({
          if (attendanceRecordId != null)
            'attendanceRecordId': attendanceRecordId,
        }),
      );

      if (response.statusCode == 200) {
        return CheckInResponse.fromJson(jsonDecode(response.body));
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Check-out failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Check-out error: $e');
      }
      rethrow;
    }
  }

  /// Validate location for check-in without actually checking in
  Future<LocationValidationResult?> validateLocationForCheckIn({
    String? attendanceSessionId,
  }) async {
    try {
      // Get current location
      final locationData = await _geolocationService.getCurrentLocation();
      if (locationData == null) {
        throw Exception('Unable to get current location');
      }

      final response = await http.post(
        Uri.parse('${AppConstants.baseUrl}/attendance/validate-location'),
        headers: await _getHeaders(),
        body: jsonEncode({
          'latitude': locationData.latitude,
          'longitude': locationData.longitude,
          'accuracy': locationData.accuracy,
          if (attendanceSessionId != null)
            'attendanceSessionId': attendanceSessionId,
        }),
      );

      if (response.statusCode == 200) {
        return LocationValidationResult.fromJson(jsonDecode(response.body));
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Location validation failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Location validation error: $e');
      }
      rethrow;
    }
  }

  /// Get live attendance for today
  Future<List<AttendanceRecord>> getLiveAttendance(String churchId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/attendance/live/$churchId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => AttendanceRecord.fromJson(json)).toList();
      } else {
        throw Exception('Failed to get live attendance');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get live attendance error: $e');
      }
      rethrow;
    }
  }

  /// Get attendance statistics
  Future<Map<String, dynamic>> getAttendanceStats(
    String churchId, {
    int days = 30,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AppConstants.baseUrl}/attendance/stats/$churchId?days=$days',
        ),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get attendance statistics');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get attendance stats error: $e');
      }
      rethrow;
    }
  }

  /// Get attendance history for current user
  Future<List<Attendance>> getMyAttendanceHistory({
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      String url = '${AppConstants.baseUrl}/attendance/my-history?limit=$limit';

      if (startDate != null) {
        url += '&startDate=${startDate.toIso8601String()}';
      }
      if (endDate != null) {
        url += '&endDate=${endDate.toIso8601String()}';
      }

      if (kDebugMode) {
        print('Fetching attendance history from: $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      if (kDebugMode) {
        print('Attendance history response status: ${response.statusCode}');
        print('Attendance history response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        if (kDebugMode) {
          print('Parsed attendance data: $data');
        }
        return data.map((json) => Attendance.fromJson(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Authentication failed. Please login again.');
      } else {
        throw Exception(
          'Failed to get attendance history: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get attendance history error: $e');
      }
      rethrow;
    }
  }

  /// Get device information for tracking
  Future<String> _getDeviceInfo() async {
    // This is a simplified version - in a real app you might want to use
    // device_info_plus package for more detailed device information
    return 'Flutter Mobile App';
  }

  /// Check if user has location permissions
  Future<bool> hasLocationPermissions() async {
    return await _geolocationService.checkAndRequestPermissions();
  }

  /// Request location permissions
  Future<bool> requestLocationPermissions() async {
    return await _geolocationService.checkAndRequestPermissions();
  }

  /// Open location settings
  Future<void> openLocationSettings() async {
    await _geolocationService.openLocationSettings();
  }

  /// Get current location for display purposes
  Future<LocationData?> getCurrentLocation() async {
    return await _geolocationService.getCurrentLocation();
  }

  /// Validate coordinates locally
  bool isValidCoordinates(double latitude, double longitude) {
    return _geolocationService.isValidCoordinates(latitude, longitude);
  }

  /// Calculate distance between two points
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return _geolocationService.calculateDistance(lat1, lon1, lat2, lon2);
  }

  /// Format distance for display
  String formatDistance(double distanceMeters) {
    return _geolocationService.formatDistance(distanceMeters);
  }

  /// Dispose resources
  void dispose() {
    _geolocationService.dispose();
  }
}
