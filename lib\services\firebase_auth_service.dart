import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/user_model.dart' as app_models;

class FirebaseAuthService extends ChangeNotifier {
  static const _storage = FlutterSecureStorage();
  static const _userDataKey = 'user_data';

  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  app_models.User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  app_models.User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  FirebaseAuthService() {
    _initializeAuthListener();
  }

  void _initializeAuthListener() {
    _firebaseAuth.authStateChanges().listen((firebase_auth.User? firebaseUser) async {
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser.uid);
      } else {
        _currentUser = null;
        _isAuthenticated = false;
        await _storage.delete(key: _userDataKey);
      }
      notifyListeners();
    });
  }

  Future<void> _loadUserData(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        userData['id'] = uid; // Ensure ID is set
        _currentUser = app_models.User.fromJson(userData);
        _isAuthenticated = true;
        
        // Cache user data locally
        await _storage.write(key: _userDataKey, value: userDoc.data().toString());
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<bool> signInWithEmailPassword(String email, String password) async {
    _setLoading(true);
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserData(credential.user!.uid);
        debugPrint('✅ Login successful for user: ${_currentUser?.email}');
        return true;
      }
      return false;
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('❌ Firebase Auth error: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      debugPrint('❌ Unexpected error during login: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signUpWithEmailPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String role = 'member',
    String? churchId,
  }) async {
    _setLoading(true);
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Create user document in Firestore
        final userData = {
          'email': email,
          'firstName': firstName,
          'lastName': lastName,
          'role': role,
          'churchId': churchId,
          'profilePicture': null,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        };

        await _firestore.collection('users').doc(credential.user!.uid).set(userData);
        await _loadUserData(credential.user!.uid);
        
        debugPrint('✅ Registration successful for user: ${_currentUser?.email}');
        return true;
      }
      return false;
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('❌ Firebase Auth error: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      debugPrint('❌ Unexpected error during registration: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> checkAuthStatus() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser.uid);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Auth status check error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      await _firebaseAuth.signOut();
      await _storage.delete(key: _userDataKey);
      
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> forgotPassword(String email) async {
    try {
      _setLoading(true);
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return true;
    } on firebase_auth.FirebaseAuthException catch (e) {
      debugPrint('Password reset error: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Password reset error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? profilePicture,
  }) async {
    if (_currentUser == null) return false;

    try {
      _setLoading(true);
      
      final updates = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };
      
      if (firstName != null) updates['firstName'] = firstName;
      if (lastName != null) updates['lastName'] = lastName;
      if (profilePicture != null) updates['profilePicture'] = profilePicture;

      await _firestore.collection('users').doc(_currentUser!.id).update(updates);
      await _loadUserData(_currentUser!.id);
      
      return true;
    } catch (e) {
      debugPrint('Profile update error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
