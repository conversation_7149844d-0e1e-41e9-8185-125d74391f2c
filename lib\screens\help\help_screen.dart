import 'package:flutter/material.dart';

import 'package:flockin_app/utils/colors.dart';
import 'package:flockin_app/utils/constants.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back),
                      color: AppColors.textPrimary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Help & Support',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                  ),
                  children: [
                    _buildHelpSection('Frequently Asked Questions', [
                      _buildFAQItem(
                        'How does automatic attendance work?',
                        'The app uses your location to automatically check you in when you arrive at church. Make sure location services are enabled.',
                      ),
                      _buildFAQItem(
                        'Why isn\'t my attendance being tracked?',
                        'Check that location permissions are granted and you\'re within the church geofence radius.',
                      ),
                      _buildFAQItem(
                        'Can I manually check in?',
                        'Yes! You can manually check in from the Attendance tab if automatic tracking isn\'t working.',
                      ),
                      _buildFAQItem(
                        'How do I update my profile?',
                        'Go to the Profile tab to view and update your personal information.',
                      ),
                    ]),
                    const SizedBox(height: 24),
                    _buildHelpSection('Contact Support', [
                      _buildContactItem(
                        Icons.email_outlined,
                        'Email Support',
                        '<EMAIL>',
                        () => _showContactDialog(context, 'Email'),
                      ),
                      _buildContactItem(
                        Icons.phone_outlined,
                        'Phone Support',
                        '+233 XX XXX XXXX',
                        () => _showContactDialog(context, 'Phone'),
                      ),
                      _buildContactItem(
                        Icons.chat_outlined,
                        'Live Chat',
                        'Chat with our support team',
                        () => _showContactDialog(context, 'Chat'),
                      ),
                    ]),
                    const SizedBox(height: 24),
                    _buildHelpSection('Resources', [
                      _buildResourceItem(
                        Icons.book_outlined,
                        'User Guide',
                        'Complete guide to using Flockin',
                        () => _showComingSoon(context, 'User Guide'),
                      ),
                      _buildResourceItem(
                        Icons.video_library_outlined,
                        'Video Tutorials',
                        'Watch how-to videos',
                        () => _showComingSoon(context, 'Video Tutorials'),
                      ),
                      _buildResourceItem(
                        Icons.bug_report_outlined,
                        'Report a Bug',
                        'Help us improve the app',
                        () => _showComingSoon(context, 'Bug Report'),
                      ),
                    ]),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHelpSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 12),
          child: Text(
            title,
            style: TextStyle(
              color: AppColors.textPrimary.withValues(alpha: 0.7),
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: Column(children: items),
        ),
      ],
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return ExpansionTile(
      title: Text(
        question,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      iconColor: AppColors.textPrimary,
      collapsedIconColor: AppColors.textPrimary.withValues(alpha: 0.7),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(
            answer,
            style: TextStyle(
              color: AppColors.textPrimary.withValues(alpha: 0.8),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactItem(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textPrimary, size: 24),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textPrimary.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: AppColors.textPrimary.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  Widget _buildResourceItem(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textPrimary, size: 24),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textPrimary.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: AppColors.textPrimary.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  void _showContactDialog(BuildContext context, String method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact via $method'),
        content: Text(
          '$method support will be available soon. Thank you for your patience!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
