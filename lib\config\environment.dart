enum Environment { development, production }

class EnvironmentConfig {
  static const Environment _currentEnvironment = Environment.production; // Change this for different builds
  
  static Environment get currentEnvironment => _currentEnvironment;
  
  static String get baseUrl {
    switch (_currentEnvironment) {
      case Environment.development:
        // Use ******** for Android emulator to reach host machine
        return 'http://********:3000/api/v1';
      case Environment.production:
        // Contabo VPS production endpoint
        return 'http://*************:3000/api/v1';
    }
  }
  
  static String get apiVersion => 'v1';
  
  static bool get isProduction => _currentEnvironment == Environment.production;
  static bool get isDevelopment => _currentEnvironment == Environment.development;
  
  // Debug settings
  static bool get enableLogging => isDevelopment;
  static bool get enableDebugMode => isDevelopment;
  
  // App configuration based on environment
  static String get appName {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'Flockin (Dev)';
      case Environment.production:
        return 'Flockin';
    }
  }
  
  // Network timeout settings
  static Duration get connectionTimeout {
    switch (_currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 30);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }
  
  static Duration get receiveTimeout {
    switch (_currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 30);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }
}
